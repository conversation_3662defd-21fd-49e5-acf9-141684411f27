package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.ProjectScore;
import org.apache.ibatis.annotations.Param;

/**
 * 项目评分表(ProjectScore)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
public interface ProjectScoreMapper {

    /**
     * 通过项目ID查询评分记录
     *
     * @param projectId 项目ID
     * @return 评分记录
     */
    ProjectScore selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 新增评分记录
     *
     * @param projectScore 评分记录
     * @return 影响行数
     */
    int insert(ProjectScore projectScore);

    /**
     * 更新评分记录
     *
     * @param projectScore 评分记录
     * @return 影响行数
     */
    int update(ProjectScore projectScore);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);
}
