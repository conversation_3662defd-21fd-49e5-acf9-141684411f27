package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysParentInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 父母信息 数据层
 */
public interface SysParentInfoMapper {
    /**
     * 查询父母信息列表
     *
     * @param sysParentInfo 父母信息
     * @return 父母信息集合
     */
    List<SysParentInfo> selectSysParentInfoList(SysParentInfo sysParentInfo);

    /**
     * 根据用户ID查询父母信息列表
     *
     * @param userId 用户ID
     * @return 父母信息列表
     */
    List<SysParentInfo> selectParentInfosByUserId(Long userId);

    /**
     * 新增父母信息
     *
     * @param sysParentInfo 父母信息
     * @return 结果
     */
    int insertSysParentInfo(SysParentInfo sysParentInfo);

    /**
     * 批量新增父母信息
     *
     * @param parentInfos 父母信息列表
     * @return 结果
     */
    int batchInsertParentInfo(@Param("list") List<SysParentInfo> parentInfos);

    /**
     * 修改父母信息
     *
     * @param sysParentInfo 父母信息
     * @return 结果
     */
    int updateSysParentInfo(SysParentInfo sysParentInfo);

    /**
     * 删除父母信息
     *
     * @param parentId 父母信息ID
     * @return 结果
     */
    int deleteSysParentInfoById(Long parentId);

    /**
     * 批量删除父母信息
     *
     * @param parentIds 需要删除的数据ID
     * @return 结果
     */
    int deleteSysParentInfoByIds(List<Long> parentIds);

    /**
     * 根据用户ID删除父母信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteSysParentInfoByUserId(Long userId);
} 