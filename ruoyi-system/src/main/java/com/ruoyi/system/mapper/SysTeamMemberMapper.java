package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysTeamMember;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队成员信息 数据层
 */
public interface SysTeamMemberMapper {
    /**
     * 查询团队成员信息列表
     *
     * @param sysTeamMember 团队成员信息
     * @return 团队成员信息集合
     */
    List<SysTeamMember> selectSysTeamMemberList(SysTeamMember sysTeamMember);

    /**
     * 根据用户ID查询团队成员列表
     *
     * @param userId 用户ID
     * @return 团队成员列表
     */
    List<SysTeamMember> selectTeamMembersByUserId(Long userId);

    /**
     * 新增团队成员信息
     *
     * @param sysTeamMember 团队成员信息
     * @return 结果
     */
    int insertSysTeamMember(SysTeamMember sysTeamMember);

    /**
     * 批量新增团队成员信息
     *
     * @param teamMembers 团队成员列表
     * @return 结果
     */
    int batchInsertTeamMember(@Param("list") List<SysTeamMember> teamMembers);

    /**
     * 修改团队成员信息
     *
     * @param sysTeamMember 团队成员信息
     * @return 结果
     */
    int updateSysTeamMember(SysTeamMember sysTeamMember);

    /**
     * 删除团队成员信息
     *
     * @param memberId 团队成员ID
     * @return 结果
     */
    int deleteSysTeamMemberById(Long memberId);

    /**
     * 批量删除团队成员信息
     *
     * @param memberIds 需要删除的数据ID
     * @return 结果
     */
    int deleteSysTeamMemberByIds(List<Long> memberIds);

    /**
     * 根据用户ID删除团队成员信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteSysTeamMemberByUserId(Long userId);

    /**
     * 根据动态条件查询团队成员信息
     * @param query 查询条件对象
     * @return 符合条件的团队成员列表
     */
    List<SysTeamMember> selectTeamMembersByCondition(SysTeamMember query);

    /**
     * 根据身份证号和用户ID查询是否存在其他团队成员
     *
     * @param idNumber 身份证号
     * @param userId 当前用户ID
     * @return 团队成员列表
     */
    List<SysTeamMember> selectByCondition(@Param("idNumber") String idNumber, @Param("userId") Long userId);

}
