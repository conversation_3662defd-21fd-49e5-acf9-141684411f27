package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.ProjectTutor;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目辅导老师信息表(ProjectTutor)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
public interface ProjectTutorMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ProjectTutor selectById(Integer id);

    /**
     * 通过项目ID查询辅导老师列表
     *
     * @param projectId 项目ID
     * @return 辅导老师列表
     */
    List<ProjectTutor> selectByProjectId(Integer projectId);

    /**
     * 查询指定行数据
     *
     * @param projectTutor 查询条件
     * @return 对象列表
     */
    List<ProjectTutor> selectList(ProjectTutor projectTutor);

    /**
     * 新增数据
     *
     * @param projectTutor 实例对象
     * @return 影响行数
     */
    int insert(ProjectTutor projectTutor);

    /**
     * 批量新增数据
     *
     * @param projectTutors 实例对象列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<ProjectTutor> projectTutors);

    /**
     * 修改数据
     *
     * @param projectTutor 实例对象
     * @return 影响行数
     */
    int update(ProjectTutor projectTutor);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 通过项目ID删除数据
     *
     * @param projectId 项目ID
     * @return 影响行数
     */
    int deleteByProjectId(Integer projectId);
} 