package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.ProjectInfo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 作品信息表(ProjectInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-27 14:31:05
 */
//@Mapper
public interface ProjectInfoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ProjectInfo queryById(Integer id);

    /**
     * 分页查询
     *
     * @param projectInfo 筛选条件
     * @return 查询结果
     */
    List<ProjectInfo> queryAllByLimit(@Param("category") Integer category, @Param("name") String name,
                                      @Param("userName") String userName, @Param("district") String district,
                                      @Param("participantType") String participantType, @Param("articleStatus") Integer articleStatus);

    List<String> queryAllUserNameByLimit(@Param("category") Integer category, @Param("name") String name,
                                     @Param("userName") String userName, @Param("district") String district,
                                     @Param("participantType") String participantType, @Param("articleStatus") Integer articleStatus);
    /**
     * 统计总行数
     *
     * @param projectInfo 查询条件
     * @return 总行数
     */
    long count(ProjectInfo projectInfo);

    /**
     * 新增数据
     *
     * @param projectInfo 实例对象
     * @return 影响行数
     */
    int insert(ProjectInfo projectInfo);


    /**
     * 修改数据
     *
     * @param projectInfo 实例对象
     * @return 影响行数
     */
    int update(ProjectInfo projectInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    void updateDeclaration(ProjectInfo projectInfo);

    void updateAudit(ProjectInfo projectInfo);

    /**
     * 更新项目平均分
     *
     * @param projectId 项目ID
     * @param averageScore 平均分
     * @return 影响行数
     */
    int updateAverageScore(@Param("projectId") Integer projectId, @Param("averageScore") BigDecimal averageScore);

}

