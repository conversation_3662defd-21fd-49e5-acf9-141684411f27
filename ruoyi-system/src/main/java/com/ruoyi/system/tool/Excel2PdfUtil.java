package com.ruoyi.system.tool;

import com.aspose.cells.*;
import com.aspose.cells.License;
import com.aspose.cells.LoadOptions;
import com.aspose.cells.PdfSaveOptions;

import java.io.*;

public class Excel2PdfUtil {
    /**
     * 加载配置文件
     */
    private static boolean getLicense() {
        boolean result = false;
        try (InputStream in = Excel2PdfUtil.class.getClassLoader().getResourceAsStream("license.xml")) {
            License license = new License();
            license.setLicense(in);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * Excel转PDF
     */
    public static File excel2Pdf(InputStream inputStream, String docPath, String pdfPath) {
        System.out.println("pdf转换中...");
        long old = System.currentTimeMillis();
        File pdfFile = new File(pdfPath);
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            if (!getLicense()) {
                throw new RuntimeException("文件转换失败!");
            }

            // 设置字体包位置 - 不同环境可用从配置文件中获取
           IndividualFontConfigs configs = new IndividualFontConfigs();
           configs.setFontFolder("/usr/share/fonts", true);
           // configs.setFontFolder("C:\\Windows\\Fonts", true);

            LoadOptions loadOptions = new LoadOptions();
            Workbook workbook;
            if (inputStream != null) {
                workbook = new Workbook(inputStream, loadOptions);
            } else {
                workbook = new Workbook(docPath, loadOptions);
            }

            // 获取第一个工作表
            Worksheet worksheet = workbook.getWorksheets().get(0);
            
            // 在合适的位置添加分页符
            // 通常表格的一半位置或者在某个逻辑分组后添加分页符
            // 这里假设在第40行添加分页符，可以根据实际情况调整
            worksheet.getHorizontalPageBreaks().add(40);

            PdfSaveOptions opts = new PdfSaveOptions();
            opts.setOnePagePerSheet(false); // 设置为false，使用我们手动添加的分页符
            workbook.save(fos, opts);

            long now = System.currentTimeMillis();
            System.out.println("pdf转换成功，共耗时：" + ((now - old) / 1000.0) + "秒");
            return pdfFile;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("文件转换失败!");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
