package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目评分表(ProjectScore)实体类
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProjectScore implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 评委1分数
     */
    private BigDecimal score1;

    /**
     * 评委1评语
     */
    private String comment1;

    /**
     * 评委1用户ID
     */
    private Long judge1Id;

    /**
     * 评委1姓名
     */
    private String judge1Name;

    /**
     * 评委1评分时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scoreTime1;

    /**
     * 评委2分数
     */
    private BigDecimal score2;

    /**
     * 评委2评语
     */
    private String comment2;

    /**
     * 评委2用户ID
     */
    private Long judge2Id;

    /**
     * 评委2姓名
     */
    private String judge2Name;

    /**
     * 评委2评分时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scoreTime2;

    /**
     * 评委3分数
     */
    private BigDecimal score3;

    /**
     * 评委3评语
     */
    private String comment3;

    /**
     * 评委3用户ID
     */
    private Long judge3Id;

    /**
     * 评委3姓名
     */
    private String judge3Name;

    /**
     * 评委3评分时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scoreTime3;

    /**
     * 平均分
     */
    private BigDecimal averageScore;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;
}
