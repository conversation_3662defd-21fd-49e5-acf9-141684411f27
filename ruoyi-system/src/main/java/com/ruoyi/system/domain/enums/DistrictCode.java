package com.ruoyi.system.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 北京市区域代码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistrictCode {

    /**
     * 东城区
     */
    DONGCHENG("110101", "110000", "东城区", 0),

    /**
     * 西城区
     */
    XICHENG("110102", "110000", "西城区", 1),

    /**
     * 朝阳区
     */
    CHAOYANG("110105", "110000", "朝阳区", 2),

    /**
     * 丰台区
     */
    FENGTAI("110106", "110000", "丰台区", 3),

    /**
     * 石景山区
     */
    SHIJINGSHAN("110107", "110000", "石景山区", 4),

    /**
     * 海淀区
     */
    HAIDIAN("110108", "110000", "海淀区", 5),

    /**
     * 门头沟区
     */
    MENTOUGOU("110109", "110000", "门头沟区", 6),

    /**
     * 房山区
     */
    FANGSHAN("110111", "110000", "房山区", 7),

    /**
     * 通州区
     */
    TONGZHOU("110112", "110000", "通州区", 8),

    /**
     * 顺义区
     */
    SHUNYI("110113", "110000", "顺义区", 9),

    /**
     * 昌平区
     */
    CHANGPING("110114", "110000", "昌平区", 10),

    /**
     * 大兴区
     */
    DAXING("110115", "110000", "大兴区", 11),

    /**
     * 怀柔区
     */
    HUAIROU("110116", "110000", "怀柔区", 12),

    /**
     * 平谷区
     */
    PINGGU("110117", "110000", "平谷区", 13),

    /**
     * 密云区
     */
    MIYUN("110118", "110000", "密云区", 14),

    /**
     * 延庆区
     */
    YANQING("110119", "110000", "延庆区", 15),

    /**
     * 经开区
     */
    JINGKAI("110120", "110000", "经开区", 16),

    /**
     * 燕山
     */
    YANSHAN("110121", "110000", "燕山", 17);

    /**
     * 区域ID
     */
    private final String id;

    /**
     * 父级ID
     */
    private final String parentId;

    /**
     * 区域名称
     */
    private final String name;

    /**
     * 权重
     */
    private final Integer weight;

    /**
     * 静态映射表，用于快速查找
     */
    private static final Map<String, DistrictCode> ID_MAP =
        Arrays.stream(values()).collect(Collectors.toMap(DistrictCode::getId, Function.identity()));

    /**
     * 根据区域ID获取枚举
     *
     * @param id 区域ID
     * @return 对应的枚举，如果不存在返回null
     */
    public static DistrictCode getById(String id) {
        return ID_MAP.get(id);
    }

    /**
     * 根据区域ID获取区域名称
     *
     * @param id 区域ID
     * @return 区域名称，如果不存在返回null
     */
    public static String getNameById(String id) {
        DistrictCode district = getById(id);
        return district != null ? district.getName() : null;
    }

    /**
     * 检查给定的ID是否是有效的区域代码（各区管理员）
     * 支持两种格式：110101 或 district_110101
     *
     * @param id 区域ID
     * @return 是否有效
     */
    public static boolean isValidDistrictId(String id) {
        if (id == null) {
            return false;
        }

        // 直接匹配格式：110101
        if (ID_MAP.containsKey(id)) {
            return true;
        }

        // 匹配带前缀格式：district_110101
        if (id.startsWith("district_")) {
            String districtCode = id.substring("district_".length());
            return ID_MAP.containsKey(districtCode);
        }

        return false;
    }

    /**
     * 检查给定的ID是否是有效的区域代码（各区审核员）
     * 支持两种格式：110101 或 recommend_110101
     *
     * @param id 区域ID
     * @return 是否有效
     */
    public static boolean isValidRecommendId(String id) {
        if (id == null) {
            return false;
        }

        // 直接匹配格式：110101
        if (ID_MAP.containsKey(id)) {
            return true;
        }

        // 匹配带前缀格式：district_110101
        if (id.startsWith("recommend_")) {
            String districtCode = id.substring("recommend_".length());
            return ID_MAP.containsKey(districtCode);
        }

        return false;
    }

    /**
     * 检查给定的ID是否是有效的区域代码（各区评委员）
     * 支持两种格式：110101 或 judge_110101
     *
     * @param id 区域ID
     * @return 是否有效
     */
    public static boolean isValidJudgeId(String id) {
        if (id == null) {
            return false;
        }

        // 直接匹配格式：110101
        if (ID_MAP.containsKey(id)) {
            return true;
        }

        // 匹配带前缀格式：judge_110101
        if (id.startsWith("judge_")) {
            String districtCode = id.substring("judge_".length());
            return ID_MAP.containsKey(districtCode);
        }

        return false;
    }

    /**
     * 获取所有区域ID
     *
     * @return 所有区域ID的数组
     */
    public static String[] getAllDistrictIds() {
        return Arrays.stream(values())
                .map(DistrictCode::getId)
                .toArray(String[]::new);
    }
}
