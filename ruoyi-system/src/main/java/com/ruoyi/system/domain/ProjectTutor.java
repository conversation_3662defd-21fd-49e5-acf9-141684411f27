package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目辅导老师信息表(ProjectTutor)实体类
 *
 * <AUTHOR>
 * @since 2025-06-01
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProjectTutor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的项目ID
     */
    private Integer projectId;

    /**
     * 辅导老师姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职务/职称
     */
    private String title;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 所属地区code
     */
    private String district;

    /**
     * 所属地区文本表示
     */
    private String districtText;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 排序序号，1为第一辅导老师，2为第二辅导老师
     */
    private Integer sortOrder;
}
