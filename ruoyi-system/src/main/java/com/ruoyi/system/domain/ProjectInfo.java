package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 作品信息表(ProjectInfo)实体类
 *
 * <AUTHOR>
 * @since 2025-05-27 14:26:43
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProjectInfo implements Serializable {
    private static final long serialVersionUID = 863148488683341815L;

    private Integer id;
    /**
     * 作品类别
     * 1 AI艺术生成 2 AI交互设计 3 AI工程实践 4 AI算法挑战 5 AI创新教学案例征集
     */
    private String category;
    /**
     * 作品名称
     */
    private String name;
    /**
     * 作品url
     */
    private String workUrl;
    /**
     * 所属区code
     */
    private String district;

    /**
     * 所属区名称
     */
    private String addressContent;
    /**
     * 辅导老师
     */
    private String tutorName;
    /**
     * 辅导老师联系方式
     */
    private String tutorContact;
    /**
     * 作品简介
     */
    private String description;
    /**
     * 创作档案/演示视频
     */
    private String creationFile;
    /**
     * 作品参赛对象 1-小学 2-普通中学/中专/职高在校学生 3-高校本科及硕士研究生 4-教师
     */
    private String participantType;
    /**
     * 参加人数
     */
    private Integer participantCount;
    /**
     * 创作者声明
     */
    private String creatorStatement;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;
    /**
     * 0 正常 1删除
     */
    private Integer isDeleted;

    /**
     * 获得分数
     */
    private Double score;

    /**
     * 打分人
     */
    private String scorer;
    /**
     * 打分时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scoreTime;

    /**
     * 平均分
     */
    private BigDecimal averageScore;

    /**
     * 作品上传者-用户账号
     */
    private String userName;

    /**
     * 申报表URL
     */
    private String declarationUrl;

    /**
     *  市级审核状态 1-通过 2-拒绝
     */
    private Integer auditStatus;

    /**
     *  市级审核备注
     */
    private String auditRemark;

    /**
     * 作品简介文本描述
     */
    private String descriptionText;

    /**
     * 市级审核员
     */
    private String auditor;

    /**
     * 作品状态 0-待提交申报表 1-待审核 2-初审通过 3-初审拒绝 4-申报成功 5-终审拒绝
     */
    private Integer articleStatus;

    /**
     * 区级审核状态（是否推荐）1-初审通过 2-初审拒绝
     */
    private Integer districtAuditStatus;

    /**
     * 区级审核备注
     */
    private String districtAuditRemark;

    /**
     * 区级审核员
     */
    private String districtAuditor;
    
    /**
     * 辅导老师列表
     */
    private List<ProjectTutor> tutors;
}

