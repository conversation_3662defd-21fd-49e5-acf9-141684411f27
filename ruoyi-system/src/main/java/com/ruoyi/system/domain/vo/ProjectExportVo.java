package com.ruoyi.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.Data;

import static com.alibaba.excel.enums.BooleanEnum.TRUE;

/**
 * 作品导出对象
 */
@Data
public class ProjectExportVo {
    @ExcelProperty("作品名称")
    private String name;

    @ExcelProperty("作品类别")
    private String category;

    @ExcelProperty("所属区名称")
    private String addressContent;

    @ExcelProperty("辅导老师")
    private String tutorName;

    @ExcelProperty("辅导老师联系方式")
    private String tutorContact;

    @ExcelProperty("作品简介")
    @ContentStyle(wrapped = TRUE)
    private String descriptionText;

    @ExcelProperty("作品参赛对象")
    private String participantType;

    @ExcelProperty("参与人数")
    private Integer participantCount;

    @ExcelProperty("作品状态")
    private String articleStatus;

    @ExcelProperty("区级审核员")
    private String districtAuditor;

    @ExcelProperty("区级审核状态")
    private String districtAuditStatus;

    @ExcelProperty("区级审核备注")
    private String districtAuditRemark;

    @ExcelProperty("市级审核员")
    private String auditor;

    @ExcelProperty("市级审核状态")
    private String auditStatus;

    @ExcelProperty("市级审核备注")
    private String auditRemark;
}
