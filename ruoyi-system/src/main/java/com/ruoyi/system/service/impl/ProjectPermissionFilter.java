package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.ProjectInfo;
import com.ruoyi.system.domain.enums.DistrictCode;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 项目权限过滤器
 * 用于根据用户权限过滤项目信息
 *
 * <AUTHOR>
 */
@Component
public class ProjectPermissionFilter {

    /**
     * 根据用户权限过滤项目列表
     *
     * @param projects 原始项目列表
     * @param loginUser 登录用户信息
     * @return 过滤后的项目列表
     */
    public List<ProjectInfo> filterProjectsByPermission(List<ProjectInfo> projects, LoginUser loginUser) {
        if (projects == null || projects.isEmpty()) {
            return projects;
        }

        // 如果是管理员，不进行过滤
        if (loginUser.getUser().isAdmin()) {
            return projects;
        }

        Set<String> permissions = loginUser.getPermissions();
        List<SysRole> roles = loginUser.getUser().getRoles();

        // 如果有北京市管理员权限但不是管理员（管理员权限优先级最高）
        if (CollectionUtils.isNotEmpty(roles)) {
            for (SysRole role : roles) {
                if (role.getRoleKey().contains("110000")) {
                    return filterByCityOnly(projects);
                }
            }
        }

        // 检查是否有区域管理员权限（区域ID）（管理员权限优先级高于业务权限）
        Set<String> roleCodes = roles.stream()
                .map(SysRole::getRoleKey)
                .collect(Collectors.toSet());
        String userDistrictCode = getUserDistrictCode(roleCodes);
        if (userDistrictCode != null) {
            return filterByDistrictOnly(projects, userDistrictCode);
        }

        // 检查是否有区域审核员权限（区域ID）（区审核员权限优先级高于业务权限）
        String userRecommendCode = getUserRecommendCode(roleCodes);
        if (userRecommendCode != null) {
            return filterByRecommendOnly(projects, userRecommendCode);
        }

        // 检查是否有区域审核员权限（区域ID）（区审核员权限优先级高于业务权限）
        String userJudgeCode = getUserJudgeCode(roleCodes);
        if (userJudgeCode != null) {
            return filterByJudgeOnly(projects, userJudgeCode);
        }

        // 如果有审核权限但不是管理员
        if (permissions.contains("system:project:audit")) {
            return filterByAuditPermission(projects);
        }

        // 如果有推荐权限但不是管理员
        if (permissions.contains("system:project:recommend")) {
            return filterByBasicCondition(projects);
        }

        return projects;
    }

    /**
     * 根据北京市管理员权限过滤项目（只过滤北京市区域，不过滤状态）
     *
     * @param projects 项目列表
     * @return 过滤后的项目列表
     */
    private List<ProjectInfo> filterByCityOnly(List<ProjectInfo> projects) {
        return projects.stream()
                .filter(project -> project.getDistrict() != null && project.getDistrict().contains("110000"))
                .collect(Collectors.toList());
    }

    /**
     * 根据区域管理员权限过滤项目（只过滤特定区域，不过滤状态）
     *
     * @param projects 项目列表
     * @param districtCode 区域代码
     * @return 过滤后的项目列表
     */
    private List<ProjectInfo> filterByDistrictOnly(List<ProjectInfo> projects, String districtCode) {
        return projects.stream()
                .filter(project -> project.getDistrict() != null && project.getDistrict().contains(districtCode))
                .collect(Collectors.toList());
    }

    /**
     * 根据区域审核员权限过滤项目
     *
     * @param projects 项目列表
     * @param districtCode 区域代码
     * @return 过滤后的项目列表
     */
    private List<ProjectInfo> filterByRecommendOnly(List<ProjectInfo> projects, String districtCode) {
        return projects.stream()
                .filter(project -> (project.getDistrict() != null && project.getDistrict().contains(districtCode))
                        && (Objects.equals(project.getArticleStatus(), 1)
                            || !Objects.equals(project.getDistrictAuditStatus(), null)))
                .collect(Collectors.toList());
    }

    /**
     * 根据区域评委员权限过滤项目
     *
     * @param projects 项目列表
     * @param districtCode 区域代码
     * @return 过滤后的项目列表
     */
    private List<ProjectInfo> filterByJudgeOnly(List<ProjectInfo> projects, String districtCode) {
        return projects.stream()
                .filter(project -> (project.getDistrict() != null && project.getDistrict().contains(districtCode))
                        && (Objects.equals(project.getArticleStatus(), 4)
                        && Objects.equals(project.getDistrictAuditStatus(), 1)
                        && Objects.equals(project.getAuditStatus(), 1)))
                .collect(Collectors.toList());
    }

    /**
     * 根据市级审核权限过滤项目
     * 只显示初审通过的作品
     *
     * @param projects 项目列表
     * @return 过滤后的项目列表
     */
    private List<ProjectInfo> filterByAuditPermission(List<ProjectInfo> projects) {
        return projects.stream()
                .filter(project -> Objects.equals(project.getDistrictAuditStatus(), 1))
                .collect(Collectors.toList());
    }

    /**
     * 获取用户的管理员区域代码权限（各区管理员）
     *
     * @param roleCodes 用户权限集合
     * @return 区域代码，如果没有找到返回null
     */
    private String getUserDistrictCode(Set<String> roleCodes) {
        // 检查用户权限中是否包含有效的区域代码
        for (String roleCode : roleCodes) {
            if (DistrictCode.isValidDistrictId(roleCode)) {
                // 如果是 district_110101 格式，提取出 110101
                if (roleCode.startsWith("district_")) {
                    return roleCode.substring("district_".length());
                }
                // 如果是 110101 格式，直接返回
                return roleCode;
            }
        }
        return null;
    }

    /**
     * 获取用户的推荐区域代码权限（各区审核员）
     *
     * @param roleCodes 用户权限集合
     * @return 区域代码，如果没有找到返回null
     */
    private String getUserRecommendCode(Set<String> roleCodes) {
        // 检查用户权限中是否包含有效的区域代码
        for (String roleCode : roleCodes) {
            if (DistrictCode.isValidRecommendId(roleCode)) {
                // 如果是 recommend_110101 格式，提取出 110101
                if (roleCode.startsWith("recommend_")) {
                    return roleCode.substring("recommend_".length());
                }
                // 如果是 110101 格式，直接返回
                return roleCode;
            }
        }
        return null;
    }

    /**
     * 获取用户的推荐区域代码权限（各区评委员）
     *
     * @param roleCodes 用户权限集合
     * @return 区域代码，如果没有找到返回null
     */
    private String getUserJudgeCode(Set<String> roleCodes) {
        // 检查用户权限中是否包含有效的区域代码
        for (String roleCode : roleCodes) {
            if (DistrictCode.isValidJudgeId(roleCode)) {
                // 如果是 judge_110101 格式，提取出 110101
                if (roleCode.startsWith("judge_")) {
                    return roleCode.substring("judge_".length());
                }
                // 如果是 110101 格式，直接返回
                return roleCode;
            }
        }
        return null;
    }

    /**
     * 基本条件过滤（用于推荐权限用户）
     *
     * @param projects 项目列表
     * @return 过滤后的项目列表
     */
    private List<ProjectInfo> filterByBasicCondition(List<ProjectInfo> projects) {
        return projects.stream()
                .filter(project -> Objects.equals(project.getArticleStatus(), 1)
                        || !Objects.equals(project.getDistrictAuditStatus(), null))
                .collect(Collectors.toList());
    }

    /**
     * 基本条件过滤（单个项目）
     *
     * @param project 项目信息
     * @return 是否符合条件
     */
    private boolean filterByBasicCondition(ProjectInfo project) {
        return Objects.equals(project.getArticleStatus(), 1)
                || !Objects.equals(project.getDistrictAuditStatus(), null);
    }
}
