package com.ruoyi.system.service;

import com.ruoyi.system.domain.ProjectTutor;

import java.util.List;

/**
 * 项目辅导老师信息表(ProjectTutor)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
public interface IProjectTutorService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ProjectTutor selectById(Integer id);

    /**
     * 通过项目ID查询辅导老师列表
     *
     * @param projectId 项目ID
     * @return 辅导老师列表
     */
    List<ProjectTutor> selectByProjectId(Integer projectId);

    /**
     * 查询指定条件的数据
     *
     * @param projectTutor 查询条件
     * @return 对象列表
     */
    List<ProjectTutor> selectList(ProjectTutor projectTutor);

    /**
     * 新增数据
     *
     * @param projectTutor 实例对象
     * @return 实例对象
     */
    ProjectTutor insert(ProjectTutor projectTutor);

    /**
     * 批量新增数据
     *
     * @param projectTutors 实例对象列表
     * @return 影响行数
     */
    int batchInsert(List<ProjectTutor> projectTutors);

    /**
     * 修改数据
     *
     * @param projectTutor 实例对象
     * @return 实例对象
     */
    ProjectTutor update(ProjectTutor projectTutor);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    /**
     * 通过项目ID删除数据
     *
     * @param projectId 项目ID
     * @return 是否成功
     */
    boolean deleteByProjectId(Integer projectId);
} 