package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.ProjectScore;

import java.math.BigDecimal;

/**
 * 项目评分表(ProjectScore)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
public interface ProjectScoreService {

    /**
     * 通过项目ID查询评分记录
     *
     * @param projectId 项目ID
     * @return 评分记录
     */
    ProjectScore selectByProjectId(Integer projectId);

    /**
     * 项目评分
     *
     * @param projectId 项目ID
     * @param score 分数
     * @param comment 评语
     * @return 评分结果
     */
    AjaxResult scoreProject(Integer projectId, BigDecimal score, String comment);

    /**
     * 通过项目ID查询评分记录（包含评委姓名）
     *
     * @param projectId 项目ID
     * @return 评分记录
     */
    ProjectScore selectByProjectIdWithJudgeNames(Integer projectId);

    /**
     * 检查当前用户是否已经对指定项目进行过评分
     *
     * @param projectId 项目ID
     * @param judgeId 评委用户ID
     * @return true-已评分，false-未评分
     */
    boolean hasJudgeScored(Integer projectId, Long judgeId);
}
