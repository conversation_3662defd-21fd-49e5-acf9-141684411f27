package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysTeamMember;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysTeamMemberMapper;
import com.ruoyi.system.service.ISysTeamMemberService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 团队成员信息 服务层实现
 */
@Service
public class SysTeamMemberServiceImpl implements ISysTeamMemberService {
    @Autowired
    private SysTeamMemberMapper sysTeamMemberMapper;

    /**
     * 查询团队成员信息列表
     *
     * @param sysTeamMember 团队成员信息
     * @return 团队成员信息集合
     */
    @Override
    public List<SysTeamMember> selectSysTeamMemberList(SysTeamMember sysTeamMember) {
        return sysTeamMemberMapper.selectSysTeamMemberList(sysTeamMember);
    }

    /**
     * 根据用户ID查询团队成员列表
     *
     * @param userId 用户ID
     * @return 团队成员列表
     */
    @Override
    public List<SysTeamMember> selectTeamMembersByUserId(Long userId) {
        return sysTeamMemberMapper.selectTeamMembersByUserId(userId);
    }

    /**
     * 新增团队成员信息
     *
     * @param sysTeamMember 团队成员信息
     * @return 结果
     */
    @Override
    public int insertSysTeamMember(SysTeamMember sysTeamMember) {
        sysTeamMember.setCreateBy(SecurityUtils.getUsername());
        return sysTeamMemberMapper.insertSysTeamMember(sysTeamMember);
    }

    /**
     * 批量新增团队成员信息
     *
     * @param teamMembers 团队成员列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertTeamMember(List<SysTeamMember> teamMembers) {
        // String username = SecurityUtils.getUsername();
        // teamMembers.forEach(member -> member.setCreateBy(username));
        return sysTeamMemberMapper.batchInsertTeamMember(teamMembers);
    }

    /**
     * 修改团队成员信息
     *
     * @param sysTeamMember 团队成员信息
     * @return 结果
     */
    @Override
    public int updateSysTeamMember(SysTeamMember sysTeamMember) {
        sysTeamMember.setUpdateBy(SecurityUtils.getUsername());
        return sysTeamMemberMapper.updateSysTeamMember(sysTeamMember);
    }

    /**
     * 删除团队成员信息
     *
     * @param memberId 团队成员ID
     * @return 结果
     */
    @Override
    public int deleteSysTeamMemberById(Long memberId) {
        return sysTeamMemberMapper.deleteSysTeamMemberById(memberId);
    }

    /**
     * 批量删除团队成员信息
     *
     * @param memberIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysTeamMemberByIds(List<Long> memberIds) {
        return sysTeamMemberMapper.deleteSysTeamMemberByIds(memberIds);
    }

    /**
     * 根据用户ID删除团队成员信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteSysTeamMemberByUserId(Long userId) {
        return sysTeamMemberMapper.deleteSysTeamMemberByUserId(userId);
    }

    @Override
    public List<SysTeamMember> selectTeamMembersByCondition(SysTeamMember query) {
        return sysTeamMemberMapper.selectTeamMembersByCondition(query);
    }

    @Override
    public boolean isIdNumberExistsInOtherTeams(String idNumber, Long currentUserId) {
        List<SysTeamMember> list = sysTeamMemberMapper.selectByCondition(idNumber, currentUserId);
        return CollectionUtils.isNotEmpty(list);
    }

}
