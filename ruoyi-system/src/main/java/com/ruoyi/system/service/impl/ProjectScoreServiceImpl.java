package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.ProjectInfo;
import com.ruoyi.system.domain.ProjectScore;
import com.ruoyi.system.mapper.ProjectInfoMapper;
import com.ruoyi.system.mapper.ProjectScoreMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ProjectInfoService;
import com.ruoyi.system.service.ProjectScoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * 项目评分表(ProjectScore)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Service("projectScoreService")
public class ProjectScoreServiceImpl implements ProjectScoreService {

    @Autowired
    private ProjectScoreMapper projectScoreMapper;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ISysUserService userService;

    /**
     * 通过项目ID查询评分记录
     *
     * @param projectId 项目ID
     * @return 评分记录
     */
    @Override
    public ProjectScore selectByProjectId(Integer projectId) {
        return projectScoreMapper.selectByProjectId(projectId);
    }

    /**
     * 项目评分
     *
     * @param projectId 项目ID
     * @param score 分数
     * @param comment 评语
     * @return 评分结果
     */
    @Override
    @Transactional
    public AjaxResult scoreProject(Integer projectId, BigDecimal score, String comment) {
        // 1. 参数校验
        if (projectId == null) {
            return AjaxResult.error("项目ID不能为空");
        }
        if (score == null) {
            return AjaxResult.error("分数不能为空");
        }
        if (score.compareTo(BigDecimal.ZERO) < 0 || score.compareTo(new BigDecimal("100")) > 0) {
            return AjaxResult.error("分数必须在0-100之间");
        }

        // 2. 检查项目是否存在
        ProjectInfo projectInfo = projectInfoService.queryById(projectId);
        if (projectInfo == null) {
            return AjaxResult.error("该项目不存在");
        }

        // 3. 获取当前评委信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long currentJudgeId = loginUser.getUserId();

        // 4. 查询或创建评分记录
        ProjectScore projectScore = projectScoreMapper.selectByProjectId(projectId);
        Date now = new Date();

        if (projectScore == null) {
            // 创建新的评分记录
            projectScore = ProjectScore.builder()
                    .projectId(projectId)
                    .score1(score)
                    .comment1(comment)
                    .judge1Id(currentJudgeId)
                    .scoreTime1(now)
                    .averageScore(score)
                    .createdTime(now)
                    .updatedTime(now)
                    .isDeleted(0)
                    .build();
            projectScoreMapper.insert(projectScore);
            return AjaxResult.success("评分成功");
        } else {
            // 检查当前评委是否已经评分
            if (Objects.equals(projectScore.getJudge1Id(), currentJudgeId)) {
                return AjaxResult.error("您已经对该项目进行过评分，不能重复评分");
            }
            if (Objects.equals(projectScore.getJudge2Id(), currentJudgeId)) {
                return AjaxResult.error("您已经对该项目进行过评分，不能重复评分");
            }
            if (Objects.equals(projectScore.getJudge3Id(), currentJudgeId)) {
                return AjaxResult.error("您已经对该项目进行过评分，不能重复评分");
            }

            // 分配到下一个评委位置
            if (projectScore.getJudge2Id() == null) {
                projectScore.setScore2(score);
                projectScore.setComment2(comment);
                projectScore.setJudge2Id(currentJudgeId);
                projectScore.setScoreTime2(now);
            } else if (projectScore.getJudge3Id() == null) {
                projectScore.setScore3(score);
                projectScore.setComment3(comment);
                projectScore.setJudge3Id(currentJudgeId);
                projectScore.setScoreTime3(now);
            } else {
                return AjaxResult.error("该项目已有三位评委评分，无法继续评分");
            }

            // 计算平均分
            BigDecimal averageScore = calculateAverageScore(projectScore);
            projectScore.setAverageScore(averageScore);
            projectScore.setUpdatedTime(now);

            projectScoreMapper.update(projectScore);

            // 如果是第三位评委完成评分，更新ProjectInfo表的平均分
            if (projectScore.getJudge3Id() != null) {
                projectInfoMapper.updateAverageScore(projectId, averageScore);
            }

            return AjaxResult.success("评分成功");
        }
    }

    /**
     * 计算平均分
     *
     * @param projectScore 评分记录
     * @return 平均分
     */
    private BigDecimal calculateAverageScore(ProjectScore projectScore) {
        BigDecimal total = BigDecimal.ZERO;
        int count = 0;

        if (projectScore.getScore1() != null) {
            total = total.add(projectScore.getScore1());
            count++;
        }
        if (projectScore.getScore2() != null) {
            total = total.add(projectScore.getScore2());
            count++;
        }
        if (projectScore.getScore3() != null) {
            total = total.add(projectScore.getScore3());
            count++;
        }

        if (count == 0) {
            return BigDecimal.ZERO;
        }

        return total.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
    }

    /**
     * 通过项目ID查询评分记录（包含评委姓名）
     *
     * @param projectId 项目ID
     * @return 评分记录
     */
    @Override
    public ProjectScore selectByProjectIdWithJudgeNames(Integer projectId) {
        ProjectScore projectScore = projectScoreMapper.selectByProjectId(projectId);
        if (projectScore != null) {
            // 设置评委1姓名
            if (projectScore.getJudge1Id() != null) {
                projectScore.setJudge1Name(getUserDisplayName(projectScore.getJudge1Id()));
            }
            // 设置评委2姓名
            if (projectScore.getJudge2Id() != null) {
                projectScore.setJudge2Name(getUserDisplayName(projectScore.getJudge2Id()));
            }
            // 设置评委3姓名
            if (projectScore.getJudge3Id() != null) {
                projectScore.setJudge3Name(getUserDisplayName(projectScore.getJudge3Id()));
            }
        }
        return projectScore;
    }

    /**
     * 获取用户显示名称（优先使用昵称，没有昵称则使用用户名）
     *
     * @param userId 用户ID
     * @return 显示名称
     */
    private String getUserDisplayName(Long userId) {
        if (userId == null) {
            return null;
        }
        try {
            com.ruoyi.common.core.domain.entity.SysUser user = userService.selectUserById(userId);
            if (user != null) {
                // 优先使用昵称，如果昵称为空则使用用户名
                if (StringUtils.hasText(user.getNickName())) {
                    return user.getNickName();
                } else {
                    return user.getUserName();
                }
            }
        } catch (Exception e) {
            // 如果查询用户失败，返回用户ID字符串
            return "用户" + userId;
        }
        return "未知用户";
    }

    /**
     * 检查当前用户是否已经对指定项目进行过评分
     *
     * @param projectId 项目ID
     * @param judgeId 评委用户ID
     * @return true-已评分，false-未评分
     */
    @Override
    public boolean hasJudgeScored(Integer projectId, Long judgeId) {
        if (projectId == null || judgeId == null) {
            return false;
        }

        ProjectScore projectScore = projectScoreMapper.selectByProjectId(projectId);
        if (projectScore == null) {
            return false;
        }

        // 检查当前用户是否已经评分
        return Objects.equals(projectScore.getJudge1Id(), judgeId) ||
               Objects.equals(projectScore.getJudge2Id(), judgeId) ||
               Objects.equals(projectScore.getJudge3Id(), judgeId);
    }
}
