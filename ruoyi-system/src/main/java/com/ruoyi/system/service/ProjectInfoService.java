package com.ruoyi.system.service;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.ProjectInfo;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * 作品信息表(ProjectInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-27 14:26:49
 */
public interface ProjectInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ProjectInfo queryById(Integer id);

    /**
     * 分页查询
     *
     * @param projectInfo 筛选条件
     * @param pageNum
     * @param pageSize
     * @return 查询结果
     */
    PageInfo<ProjectInfo> queryByPage(Integer pageNum, Integer pageSize,Integer category,
                                      String name, String userName, String district,
                                      String participantType, Integer articleStatus,
                                      String sortField, String sortOrder);

    /**
     * 新增数据
     *
     * @param projectInfo 实例对象
     * @return 实例对象
     */
    AjaxResult insert(ProjectInfo projectInfo);

    /**
     * 修改数据
     *
     * @param projectInfo 实例对象
     * @return 实例对象
     */
    ProjectInfo update(ProjectInfo projectInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    /**
     * 修改申报信息
     * @param projectInfo 申报信息
     */
    void updateDeclaration(ProjectInfo projectInfo);

    /**
     * 修改审核信息
     * @param projectInfo 审核信息
     */
    void updateAudit(ProjectInfo projectInfo);

    /**
     * 下载申报表模板
     * @param projectInfo 项目信息
     * @param user 用户信息
     * @return
     */
    ResponseEntity<ByteArrayResource> downByProjectId(ProjectInfo projectInfo, SysUser user);

    /**
     * 导出
     * @param projectInfoList 项目信息
     * @return
     */
    ResponseEntity<ByteArrayResource> export(List<ProjectInfo> projectInfoList);
}
