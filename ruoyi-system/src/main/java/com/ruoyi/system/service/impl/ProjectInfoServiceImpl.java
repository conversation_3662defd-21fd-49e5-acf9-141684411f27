package com.ruoyi.system.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.data.ImageData;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.List;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import java.util.List;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysParentInfo;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysTeamMember;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.ProjectInfo;
import com.ruoyi.system.domain.ProjectTutor;
import com.ruoyi.system.domain.vo.ProjectExportVo;
import com.ruoyi.system.mapper.ProjectInfoMapper;
import com.ruoyi.system.service.IProjectTutorService;
import com.ruoyi.system.service.ISysTeamMemberService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ProjectInfoService;
import com.ruoyi.system.tool.Excel2PdfUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.net.URLEncoder;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 作品信息表(ProjectInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27 14:26:49
 */
@Service("projectInfoService")
public class ProjectInfoServiceImpl implements ProjectInfoService {

    private static final Logger log = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    public static final String NO_EXIST = "0";

    public static final String EXIST = "1";

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysTeamMemberService teamMemberService;

    @Autowired
    private IProjectTutorService projectTutorService;

    @Autowired
    private ProjectPermissionFilter projectPermissionFilter;

    @Value("${file.declarationPath}")
    private String declarationPath;

    @Value("${file.excelTempPath}")
    private String excelTempPath;

    @Value("${file.pdfTempPath}")
    private String pdfTempPath;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ProjectInfo queryById(Integer id) {
        ProjectInfo projectInfo = this.projectInfoMapper.queryById(id);
        if (projectInfo != null) {
            // 查询辅导老师信息
            List<ProjectTutor> tutors = projectTutorService.selectByProjectId(id);
            projectInfo.setTutors(tutors);
        }
        return projectInfo;
    }

    /**
     * 分页查询
     *
     * @param pageNum
     * @param pageSize
     * @return 查询结果
     */
    @Override
    public PageInfo<ProjectInfo> queryByPage(Integer pageNum, Integer pageSize, Integer category,
                                             String name, String userName, String district,
                                             String participantType, Integer articleStatus,
                                             String sortField, String sortOrder) {
        // 普通用户角色才查询指定用户名称，非普通查询所有
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<SysRole> roles = loginUser.getUser().getRoles();
        if (CollectionUtils.isNotEmpty(roles)) {
            boolean hasCommonRole = roles.stream()
                    .anyMatch(role -> role.getRoleKey().contains("common"));
            if (!hasCommonRole) {
                userName = null;
            }
        }

        // 查询所有符合条件的数据（不分页）
        List<ProjectInfo> allProjects = this.projectInfoMapper.queryAllByLimit(category, name, userName, district, participantType, articleStatus);

        // 根据权限进行数据过滤
        List<ProjectInfo> filteredProjects = projectPermissionFilter.filterProjectsByPermission(allProjects, loginUser);

        if ("averageScore".equals(sortField) && sortOrder != null) {
            if ("asc".equals(sortOrder)) {
                filteredProjects.sort((a, b) -> {
                    BigDecimal scoreA = a.getAverageScore() != null ? a.getAverageScore() : BigDecimal.ZERO;
                    BigDecimal scoreB = b.getAverageScore() != null ? b.getAverageScore() : BigDecimal.ZERO;
                    return scoreA.compareTo(scoreB);
                });
            } else if ("desc".equals(sortOrder)) {
                filteredProjects.sort((a, b) -> {
                    BigDecimal scoreA = a.getAverageScore() != null ? a.getAverageScore() : BigDecimal.ZERO;
                    BigDecimal scoreB = b.getAverageScore() != null ? b.getAverageScore() : BigDecimal.ZERO;
                    return scoreB.compareTo(scoreA);
                });
            }
        }

        // 手动分页处理
        int total = filteredProjects.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<ProjectInfo> pageData;
        if (startIndex < total) {
            pageData = filteredProjects.subList(startIndex, endIndex);
        } else {
            pageData = new ArrayList<>();
        }

        // 查询每个项目的辅导老师信息
        if (CollectionUtils.isNotEmpty(pageData)) {
            for (ProjectInfo projectInfo : pageData) {
                List<ProjectTutor> tutors = projectTutorService.selectByProjectId(projectInfo.getId());
                projectInfo.setTutors(tutors);
            }
        }

        // 手动创建PageInfo对象
        PageInfo<ProjectInfo> page = new PageInfo<>(pageData);
        page.setTotal(total);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setPages((int) Math.ceil((double) total / pageSize));
        return page;
    }

    /**
     * 新增数据
     *
     * @param projectInfo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public AjaxResult insert(ProjectInfo projectInfo) {
        // 作品简介文本描述不能超过300字
        if (StringUtils.length(projectInfo.getDescriptionText()) > 300) {
            return AjaxResult.error("作品简介文本描述不能超过300字");
        }

        //判断作品名称是否重复
        if (this.projectInfoMapper.queryAllByLimit(null, projectInfo.getName(), null, null, null, null) != null) {
            AjaxResult.error("作品名称重复");
        }

        // 判断该用户是否已经提交过该类型的作品
        String userName = projectInfo.getUserName();
//        List<ProjectInfo> projectInfos = projectInfoMapper.queryAllByLimit(null, null, null, null, null, null);
        List<String> userNameList = projectInfoMapper.queryAllUserNameByLimit(null, null, null, null, null, null);
        if (userNameList == null){
            userNameList = new ArrayList<>();
        }
        Set<String> userNamesSet = new HashSet<>(userNameList);
        if (userNamesSet.contains(userName)){
            return AjaxResult.error("一个用户只能参与一个活动");
        }
//        List<String> userNames = new ArrayList<>(16);
//        if (CollectionUtils.isNotEmpty(projectInfos)) {
//            for (ProjectInfo info : projectInfos) {
//                if (info.getUserName().equals(userName)) {
//                    return AjaxResult.error("一个用户只能参与一个活动");
//                }
//                userNames.add(info.getUserName());
//            }
//        }

        // 如果该用户的身份证号已在其他作品的团队成员中，则禁止（一个用户只能参与一个活动）
        SysUser user = userService.selectUserByUserName(userName);
        String idNumber = user.getIDNumber();

        Set<Long> userIds = new HashSet<>(16);
        if (CollectionUtils.isNotEmpty(userNamesSet)) {
            for (String name : userNamesSet) {
                SysUser projectUser = userService.selectUserByUserName(name);
                if (projectUser == null){
                    log.error("projectUser is null, name={}", name);
                    continue;
                }
                Long userId = projectUser.getUserId();
                userIds.add(userId);
            }
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                List<SysTeamMember> sysTeamMembers = teamMemberService.selectTeamMembersByUserId(userId);
                if (sysTeamMembers == null){
                    continue;
                }
                for (SysTeamMember teamMember : sysTeamMembers) {
                    if (teamMember.getIdNumber().equals(idNumber)) {
                        return AjaxResult.error("您已在其他团队中有提交作品");
                    }
                }
            }
        }

        projectInfo.setCreatedTime(new Date());
        projectInfo.setUpdatedTime(new Date());
        projectInfo.setIsDeleted(0);
        projectInfo.setArticleStatus(0);
        this.projectInfoMapper.insert(projectInfo);

        // 处理辅导老师信息
        List<ProjectTutor> tutors = projectInfo.getTutors();
        if (CollectionUtils.isNotEmpty(tutors)) {
            // 设置项目ID
            for (int i = 0; i < tutors.size(); i++) {
                ProjectTutor tutor = tutors.get(i);
                tutor.setProjectId(projectInfo.getId());
                tutor.setSortOrder(i + 1);
            }
            projectTutorService.batchInsert(tutors);

            // 设置兼容旧版的tutorName和tutorContact
            if (tutors.size() > 0) {
                ProjectTutor firstTutor = tutors.get(0);
                projectInfo.setTutorName(firstTutor.getName());
                projectInfo.setTutorContact(firstTutor.getContact());
                this.projectInfoMapper.update(projectInfo);
            }
        }

        return AjaxResult.success("新增成功", projectInfo);
    }

    /**
     * 修改数据
     *
     * @param projectInfo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public ProjectInfo update(ProjectInfo projectInfo) {
        projectInfo.setUpdatedTime(new Date());
        this.projectInfoMapper.update(projectInfo);

        // 处理辅导老师信息
        List<ProjectTutor> tutors = projectInfo.getTutors();
        if (tutors != null) {
            // 先删除原有的辅导老师信息
            projectTutorService.deleteByProjectId(projectInfo.getId());

            if (CollectionUtils.isNotEmpty(tutors)) {
                // 设置项目ID
                for (int i = 0; i < tutors.size(); i++) {
                    ProjectTutor tutor = tutors.get(i);
                    tutor.setProjectId(projectInfo.getId());
                    tutor.setSortOrder(i + 1);
                }
                projectTutorService.batchInsert(tutors);

                // 设置兼容旧版的tutorName和tutorContact
                if (tutors.size() > 0) {
                    ProjectTutor firstTutor = tutors.get(0);
                    projectInfo.setTutorName(firstTutor.getName());
                    projectInfo.setTutorContact(firstTutor.getContact());
                    this.projectInfoMapper.update(projectInfo);
                }
            }
        }

        return this.queryById(projectInfo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteById(Integer id) {
        ProjectInfo projectInfo = this.projectInfoMapper.queryById(id);
        if (projectInfo == null) {
            throw new RuntimeException("作品不存在");
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<SysRole> roles = loginUser.getUser().getRoles();
        for (SysRole role : roles) {
            if (StringUtils.equals(role.getRoleKey(), "common") && !Objects.equals(projectInfo.getArticleStatus(), 0)) {
                throw new RuntimeException("作品已进入审核流程，禁止删除");
            }
        }

        // 删除关联的辅导老师信息
        projectTutorService.deleteByProjectId(id);
        return this.projectInfoMapper.deleteById(id) > 0;
    }

    @Override
    public void updateDeclaration(ProjectInfo projectInfo) {
        projectInfoMapper.updateDeclaration(projectInfo);
    }

    @Override
    public void updateAudit(ProjectInfo projectInfo) {
        projectInfoMapper.updateAudit(projectInfo);
    }

    @Override
    public ResponseEntity<ByteArrayResource> downByProjectId(ProjectInfo projectInfo, SysUser user) {
        try {
            // 模板文件路径
            if (StringUtils.isBlank(declarationPath)) {
                declarationPath = "/files/declaration.xlsx";
            }
            String templatePath = declarationPath;
            File templateFile = new File(templatePath);
            if (!templateFile.exists()) {
                log.error("找不到模板文件: {}", templatePath);
                return ResponseEntity.status(500).build();
            }

            // 准备数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("addressContent", projectInfo.getAddressContent());
            dataMap.put("name", user.getNickName());
            dataMap.put("gender", user.getSex().equals("0") ? "男" : "女");
            dataMap.put("idNumber", user.getIDNumber());
            dataMap.put("school", user.getSchool());
            dataMap.put("grade", getGradeChinese(user.getGrade()));
            dataMap.put("phone", user.getPhonenumber());
            dataMap.put("email", user.getEmail());
            dataMap.put("projectName", projectInfo.getName());
            dataMap.put("category", projectInfo.getCategory());
            dataMap.put("descriptionText", projectInfo.getDescriptionText());
            dataMap.put("schoolAddress", user.getSchoolAddress());

            dataMap.put("creationFile", projectInfo.getCreationFile());
            dataMap.put("workUrl", projectInfo.getWorkUrl());
            dataMap.put("schoolManager", user.getSchoolManager());

            dataMap.put("nationality", user.getNationality());
            dataMap.put("mailingAddress", user.getMailingAddress());
            String birthDate = user.getDateOfBirth();
            if (StringUtils.isNotBlank(birthDate)) {
                // 使用 substring 截取前7位（"2025-01"）
                dataMap.put("birthDate", birthDate.substring(0, 7));
            }

            // 添加父母信息
            String noInfo = "无";
            if (CollectionUtils.isNotEmpty(user.getParents())) {
                List<SysParentInfo> parents = user.getParents();
                for (SysParentInfo parent : parents) {
                    // 如果是老师则父母项填无
                    if (StringUtils.equals(user.getGrade(), "4")) {
                        dataMap.put("fatherName", noInfo);
                        dataMap.put("fatherWorkplace", noInfo);
                        dataMap.put("fatherPhone", noInfo);
                        dataMap.put("motherName", noInfo);
                        dataMap.put("motherWorkplace", noInfo);
                        dataMap.put("motherPhone", noInfo);
                    } else {
                        if ("0".equals(parent.getRelationType())) {
                            dataMap.put("fatherName", parent.getName());
                            dataMap.put("fatherWorkplace", parent.getWorkplace());
                            dataMap.put("fatherPhone", parent.getPhone());
                        } else {
                            dataMap.put("motherName", parent.getName());
                            dataMap.put("motherWorkplace", parent.getWorkplace());
                            dataMap.put("motherPhone", parent.getPhone());
                        }
                    }
                }
            }

            // 添加辅导老师信息
            List<ProjectTutor> tutors = projectInfo.getTutors();
            if (CollectionUtils.isNotEmpty(tutors)) {
                for (int i = 1; i < tutors.size() + 1; i++) {
                    ProjectTutor tutor = tutors.get(i - 1);
                    dataMap.put("tutor_name_" + i, tutor.getName());
                    dataMap.put("tutor_contact_" + i, tutor.getContact());
                    dataMap.put("tutor_gender_" + i, tutor.getGender());
                    dataMap.put("tutor_email_" + i, tutor.getEmail());
                    dataMap.put("tutor_title_" + i, tutor.getTitle());
                    dataMap.put("tutor_workUnit_" + i, tutor.getWorkUnit());
                    dataMap.put("tutor_districtText_" + i, tutor.getDistrictText());
                }
            }

            // 处理用户头像
            if (StringUtils.isNotBlank(user.getAvatar())) {
                try {
                    WriteCellData<Void> writeCellData = new WriteCellData<>();
                    ImageData imageData = new ImageData();

                    // 获取图片字节数组
                    byte[] imageBytes = getImageBytesFromUrl(user.getAvatar());
                    if (imageBytes != null) {
                        imageData.setImage(imageBytes);
                        // 设置图片在单元格中的位置
                        imageData.setRelativeFirstRowIndex(0);
                        imageData.setRelativeLastRowIndex(2);
                        imageData.setRelativeFirstColumnIndex(0);
                        imageData.setRelativeLastColumnIndex(1);

                        imageData.setTop(1);
                        imageData.setBottom(1);
                        imageData.setLeft(1);
                        imageData.setRight(1);

                        writeCellData.setImageDataList(Arrays.asList(imageData));
                        dataMap.put("image", writeCellData);
                    }
                } catch (Exception e) {
                    log.error("处理用户头像失败", e);
                }
            }

            // 设置团队信息
            List<SysTeamMember> teamMembers = user.getTeamMembers();
            if (CollectionUtils.isNotEmpty(teamMembers)) {
                for (int i = 1; i < teamMembers.size() + 1; i++) {
                    SysTeamMember teamMember = teamMembers.get(i - 1);
                    dataMap.put("team_name_" + i, teamMember.getName());
                    dataMap.put("team_gender_" + i, teamMember.getSex().equals("0") ? "男" : "女");
                    dataMap.put("team_idNumber_" + i, teamMember.getIdNumber());
                    dataMap.put("team_school_" + i, teamMember.getSchool());
                    dataMap.put("team_grade_" + i, getGradeChinese(teamMember.getGrade()));
                    dataMap.put("team_phone_" + i, teamMember.getPhonenumber());
                    dataMap.put("team_email_" + i, teamMember.getEmail());

                    dataMap.put("team_nationality_" + i, teamMember.getNationality());
                    dataMap.put("team_birthDate_" + i, teamMember.getDateOfBirth());
                    dataMap.put("team_mailingAddress_" + i, teamMember.getMailingAddress());
                    dataMap.put("team_schoolAddress_" + i, teamMember.getSchoolAddress());
                    dataMap.put("team_email_" + i, teamMember.getEmail());
                    dataMap.put("team_gradeText_" + i, teamMember.getGradeText());

                    // 父母信息
                    if (StringUtils.equals(teamMember.getGrade(), "4")) {
                        dataMap.put("team_fatherName_" + i, noInfo);
                        dataMap.put("team_fatherWorkplace_" + i, noInfo);
                        dataMap.put("team_fatherPhone_" + i, noInfo);
                        dataMap.put("team_motherName_" + i, noInfo);
                        dataMap.put("team_motherWorkplace_" + i, noInfo);
                        dataMap.put("team_motherPhone_" + i, noInfo);
                    } else {
                        dataMap.put("team_fatherName_" + i, teamMember.getFatherName());
                        dataMap.put("team_fatherWorkplace_" + i, teamMember.getFatherWorkplace());
                        dataMap.put("team_fatherPhone_" + i, teamMember.getFatherPhone());
                        dataMap.put("team_motherName_" + i, teamMember.getMotherName());
                        dataMap.put("team_motherWorkplace_" + i, teamMember.getMotherWorkplace());
                        dataMap.put("team_motherPhone_" + i, teamMember.getMotherPhone());
                    }

                    // 处理团队成员头像
                    if (StringUtils.isNotBlank(teamMember.getAvatar())) {
                        try {
                            WriteCellData<Void> teamWriteCellData = new WriteCellData<>();
                            ImageData teamImageData = new ImageData();

                            byte[] teamImageBytes = getImageBytesFromUrl(teamMember.getAvatar());
                            if (teamImageBytes != null) {
                                teamImageData.setImage(teamImageBytes);
                                teamImageData.setRelativeFirstRowIndex(0);
                                teamImageData.setRelativeLastRowIndex(2);
                                teamImageData.setRelativeFirstColumnIndex(0);
                                teamImageData.setRelativeLastColumnIndex(1);

                                teamImageData.setTop(1);
                                teamImageData.setBottom(1);
                                teamImageData.setLeft(1);
                                teamImageData.setRight(1);

                                teamWriteCellData.setImageDataList(Arrays.asList(teamImageData));
                                dataMap.put("team_image_" + i, teamWriteCellData);
                            }
                        } catch (Exception e) {
                            log.error("处理团队成员头像失败", e);
                        }
                    }
                }
            }

            // 处理作品类别复选框
            setCategoryCheckboxValues(dataMap, projectInfo.getCategory());

            // 使用EasyExcel填充模板
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 创建ExcelWriter
            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .withTemplate(templatePath)
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            // 为特定字段设置自动换行
            // 对于team_grade_1字段，创建一个支持换行的单元格数据
            // if (dataMap.containsKey("team_grade_1")) {
            //     Object gradeValue = dataMap.get("team_grade_1");
            //     if (gradeValue != null) {
            //         WriteCellData<String> cellData = new WriteCellData<>(gradeValue.toString());
            //         WriteCellStyle cellStyle = new WriteCellStyle();
            //         cellStyle.setWrapped(true);
            //         cellData.setWriteCellStyle(cellStyle);
            //         dataMap.put("team_grade_1", cellData);
            //     }
            // }
            // if (dataMap.containsKey("team_grade_2")) {
            //     Object gradeValue = dataMap.get("team_grade_2");
            //     if (gradeValue != null) {
            //         WriteCellData<String> cellData = new WriteCellData<>(gradeValue.toString());
            //         WriteCellStyle cellStyle = new WriteCellStyle();
            //         cellStyle.setWrapped(true);
            //         cellData.setWriteCellStyle(cellStyle);
            //         dataMap.put("team_grade_2", cellData);
            //     }
            // }
            // if (dataMap.containsKey("grade")) {
            //     Object gradeValue = dataMap.get("grade");
            //     if (gradeValue != null) {
            //         WriteCellData<String> cellData = new WriteCellData<>(gradeValue.toString());
            //         WriteCellStyle cellStyle = new WriteCellStyle();
            //         cellStyle.setWrapped(true);
            //         cellData.setWriteCellStyle(cellStyle);
            //         dataMap.put("grade", cellData);
            //     }
            // }

            // 填充数据
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(dataMap, fillConfig, writeSheet);

            // 处理用户头像
            if (StringUtils.isNotBlank(user.getAvatar())) {
                try {
                    // 添加图片
                    BufferedImage image = getImageFromUrl(user.getAvatar());
                    if (image != null) {
                        // 将图片转换为字节数组
                        ByteArrayOutputStream imageBytes = new ByteArrayOutputStream();
                        ImageIO.write(image, getImageFormat(user.getAvatar()), imageBytes);

                        // 直接将图片字节数组添加到数据Map中
                        dataMap.put("avatar", imageBytes.toByteArray());
                    }
                } catch (Exception e) {
                    log.error("处理用户头像失败", e);
                    // 继续执行，不因头像处理失败而中断整个导出过程
                }
            }

            // 完成并关闭
            excelWriter.finish();

            // 构建响应
            long currentTimeMillis = System.currentTimeMillis();
            byte[] bytes = outputStream.toByteArray();
            String filename = "作品申报表" + currentTimeMillis + ".xlsx";

            // ===== 新增：保存到本地磁盘 ===== 本地测试
            // String outputDirPath = "/Users/<USER>/Documents/temp/0615/demo-pdf/file-temp/";
            String outputDirPath = StringUtils.isEmpty(excelTempPath) ? "/files/temp/excel/" : excelTempPath;
            File outputDir = new File(outputDirPath);
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            log.info("System encoding: {}", Charset.defaultCharset().displayName());
            File outputFile = new File(outputDir, filename);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                fos.write(bytes);
                log.info("文件已成功保存至本地路径: {}", outputFile.getAbsolutePath());
            } catch (IOException e) {
                log.error("写入文件到本地失败", e);
            }

            // 转换pdf
            String filePath = outputFile.getAbsolutePath();
            File excelFile = new File(filePath);
            if (!excelFile.exists()){
                throw new FileNotFoundException(filePath + "not found");
            }

            // macos 地址 本地测试
            // String pdfPath = "/Users/<USER>/Documents/temp/0615/demo-pdf/file-temp/";
            // linux服务器地址
            String pdfPath = StringUtils.isEmpty(pdfTempPath) ? "/files/temp/pdf/" : pdfTempPath;
            Excel2PdfUtil.excel2Pdf(null, excelFile.getAbsolutePath(), pdfPath + "作品信息表" + currentTimeMillis + ".pdf");

            // 读取生成的PDF文件内容
            File pdfFile = new File(pdfPath + "作品信息表" + currentTimeMillis + ".pdf");
            if (!pdfFile.exists()) {
                throw new FileNotFoundException("生成的PDF文件未找到");
            }

            byte[] pdfBytes;
            try (FileInputStream fis = new FileInputStream(pdfFile)) {
                pdfBytes = new byte[(int) pdfFile.length()];
                fis.read(pdfBytes);
            }

            String pdfFilename = "作品信息表_" + user.getNickName() + ".pdf";

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + URLEncoder.encode(pdfFilename, StandardCharsets.UTF_8.toString()) + "\"")
                    .contentType(MediaType.APPLICATION_PDF)
                    .contentLength(pdfBytes.length)
                    .body(new ByteArrayResource(pdfBytes));

        } catch (Exception e) {
            log.error("PDF文档生成失败", e);
            return ResponseEntity.internalServerError()
                    .body(new ByteArrayResource(("PDF文档生成失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8)));
        }
    }

    @Override
    public ResponseEntity<ByteArrayResource> export(List<ProjectInfo> projectInfoList) {
        // 准备导出数据
        List<ProjectExportVo> exportList = new ArrayList<>();
        for (ProjectInfo info : projectInfoList) {
            ProjectExportVo vo = new ProjectExportVo();
            vo.setName(info.getName());
            vo.setCategory(getCategoryName(info.getCategory()));
            vo.setAddressContent(info.getAddressContent());
            vo.setTutorName(info.getTutorName());
            vo.setTutorContact(info.getTutorContact());
            vo.setDescriptionText(info.getDescriptionText());
            vo.setParticipantType(getParticipantTypeName(info.getParticipantType()));
            vo.setParticipantCount(info.getParticipantCount());
            vo.setArticleStatus(getArticleStatusName(info.getArticleStatus()));
            vo.setDistrictAuditor(info.getDistrictAuditor());
            vo.setDistrictAuditStatus(getDistrictAuditStatusName(info.getDistrictAuditStatus()));
            vo.setDistrictAuditRemark(info.getDistrictAuditRemark());
            vo.setAuditor(info.getAuditor());
            vo.setAuditStatus(getAuditStatusName(info.getAuditStatus()));
            vo.setAuditRemark(info.getAuditRemark());
            exportList.add(vo);
        }

        try {
            // 生成Excel
            org.apache.commons.io.output.ByteArrayOutputStream outputStream = new org.apache.commons.io.output.ByteArrayOutputStream();

            // 设置表头样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short)14); // 表头字体大小
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);

            // 设置内容样式（可选）
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short)12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 移除全局自动换行设置
            // contentWriteCellStyle.setWrapped(true);

            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            EasyExcel.write(outputStream, ProjectExportVo.class)
                    .registerWriteHandler(styleStrategy)
                    // 自动调整列宽
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 设置每列最小宽度
                    .registerWriteHandler(new SheetWriteHandler() {
                        @Override
                        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                            // 设置每列的宽度
                            for (int i = 0; i < 15; i++) {
                                // 作品简介文本描述列设置更宽
                                if (i == 5) {
                                    writeSheetHolder.getSheet().setColumnWidth(i, 50 * 256); // 50个字符宽度
                                } else {
                                    writeSheetHolder.getSheet().setColumnWidth(i, 20 * 256); // 20个字符宽度
                                }
                            }
                        }

                        @Override
                        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                            // 不需要实现
                        }
                    })
                    .sheet("作品信息")
                    .doWrite(exportList);

            // 构建文件下载响应
            String filename = "作品信息表_" + System.currentTimeMillis() + ".xlsx";
            byte[] bytes = outputStream.toByteArray();

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()) + "\"")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(bytes.length)
                    .body(new ByteArrayResource(bytes));
        } catch (Exception e) {
            log.error("导出作品信息失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 设置作品类别复选框值
     */
    private void setCategoryCheckboxValues(Map<String, Object> dataMap, String category) {
        // 使用demo中的方式，默认所有复选框为未选中状态(□)
        dataMap.put("artGeneration", "□");
        dataMap.put("engineeringPractice", "□");
        dataMap.put("interactionDesign", "□");
        dataMap.put("algorithmChallenge", "□");
        dataMap.put("caseCollection", "□");

        // 建立中文到英文字段的映射关系
        if (category != null) {
            String englishFieldName = getCategoryEnglishName(category);
            if (englishFieldName != null) {
                dataMap.put(englishFieldName, "■");
            }
        }
    }

    // 添加中文到英文字段名的映射方法
    private String getCategoryEnglishName(String chineseCategory) {
        switch (chineseCategory) {
            case "AI艺术生成":
                return "artGeneration";
            case "AI工程实践":
                return "engineeringPractice";
            case "AI交互设计":
                return "interactionDesign";
            case "AI算法挑战":
                return "algorithmChallenge";
            case "AI创新教学案例征集":
                return "caseCollection";
            default:
                log.warn("未知的作品类别: {}", chineseCategory);
                return null;
        }
    }

    /**
     * 获取年级中文描述
     */
    private String getGradeChinese(String grade) {
        switch (grade) {
            case "1":
                return "小学";
            case "2":
                return "普通中学/中专/职高在校学生";
            case "3":
                return "高校本科及硕士研究生";
            case "4":
                return "教师";
            default:
                return grade;
        }
    }

    /**
     * 从URL获取图片
     */
    private BufferedImage getImageFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            return ImageIO.read(url);
        } catch (IOException e) {
            log.error("获取图片失败: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 获取图片格式
     */
    private String getImageFormat(String imageUrl) {
        String format = "png"; // 默认格式
        if (StringUtils.isNotBlank(imageUrl)) {
            String lowerUrl = imageUrl.toLowerCase();
            if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
                format = "jpg";
            } else if (lowerUrl.endsWith(".png")) {
                format = "png";
            } else if (lowerUrl.endsWith(".gif")) {
                format = "gif";
            }
        }
        return format;
    }

    private byte[] getImageBytesFromUrl(String imageUrl) {
        try (InputStream in = new URL(imageUrl).openStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return out.toByteArray();
        } catch (Exception e) {
            log.error("获取图片失败: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 获取作品类别中文名称
     */
    private String getCategoryName(String category) {
        if (org.apache.commons.lang3.StringUtils.isBlank(category)) {
            return "";
        }
        // 作品类别已在查询时转换为中文，直接返回
        return category;
    }

    /**
     * 获取参赛对象中文名称
     */
    private String getParticipantTypeName(String participantType) {
        if (org.apache.commons.lang3.StringUtils.isBlank(participantType)) {
            return "";
        }
        switch (participantType) {
            case "1":
                return "小学";
            case "2":
                return "普通中学/中专/职高在校学生";
            case "3":
                return "高校本科及硕士研究生";
            case "4":
                return "教师";
            default:
                return participantType;
        }
    }

    /**
     * 获取作品状态中文名称
     */
    private String getArticleStatusName(Integer articleStatus) {
        if (articleStatus == null) {
            return "";
        }
        switch (articleStatus) {
            case 0:
                return "待提交申报表";
            case 1:
                return "待审核";
            case 2:
                return "初审通过";
            case 3:
                return "初审拒绝";
            case 4:
                return "申报成功";
            case 5:
                return "终审拒绝";
            default:
                return String.valueOf(articleStatus);
        }
    }

    /**
     * 获取区级审核状态中文名称
     */
    private String getDistrictAuditStatusName(Integer districtAuditStatus) {
        if (districtAuditStatus == null) {
            return "";
        }
        switch (districtAuditStatus) {
            case 1:
                return "初审通过";
            case 2:
                return "初审拒绝";
            default:
                return String.valueOf(districtAuditStatus);
        }
    }

    /**
     * 获取市级审核状态中文名称
     */
    private String getAuditStatusName(Integer auditStatus) {
        if (auditStatus == null) {
            return "";
        }
        switch (auditStatus) {
            case 1:
                return "通过";
            case 2:
                return "拒绝";
            default:
                return String.valueOf(auditStatus);
        }
    }

}
