package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysParentInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysParentInfoMapper;
import com.ruoyi.system.service.ISysParentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 父母信息 服务层实现
 */
@Service
public class SysParentInfoServiceImpl implements ISysParentInfoService {
    @Autowired
    private SysParentInfoMapper sysParentInfoMapper;

    /**
     * 查询父母信息列表
     *
     * @param sysParentInfo 父母信息
     * @return 父母信息集合
     */
    @Override
    public List<SysParentInfo> selectSysParentInfoList(SysParentInfo sysParentInfo) {
        return sysParentInfoMapper.selectSysParentInfoList(sysParentInfo);
    }

    /**
     * 根据用户ID查询父母信息列表
     *
     * @param userId 用户ID
     * @return 父母信息列表
     */
    @Override
    public List<SysParentInfo> selectParentInfosByUserId(Long userId) {
        return sysParentInfoMapper.selectParentInfosByUserId(userId);
    }

    /**
     * 新增父母信息
     *
     * @param sysParentInfo 父母信息
     * @return 结果
     */
    @Override
    public int insertSysParentInfo(SysParentInfo sysParentInfo) {
        sysParentInfo.setCreateBy(SecurityUtils.getUsername());
        return sysParentInfoMapper.insertSysParentInfo(sysParentInfo);
    }

    /**
     * 批量新增父母信息
     *
     * @param parentInfos 父母信息列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertParentInfo(List<SysParentInfo> parentInfos) {
        return sysParentInfoMapper.batchInsertParentInfo(parentInfos);
    }

    /**
     * 修改父母信息
     *
     * @param sysParentInfo 父母信息
     * @return 结果
     */
    @Override
    public int updateSysParentInfo(SysParentInfo sysParentInfo) {
        sysParentInfo.setUpdateBy(SecurityUtils.getUsername());
        return sysParentInfoMapper.updateSysParentInfo(sysParentInfo);
    }

    /**
     * 删除父母信息
     *
     * @param parentId 父母信息ID
     * @return 结果
     */
    @Override
    public int deleteSysParentInfoById(Long parentId) {
        return sysParentInfoMapper.deleteSysParentInfoById(parentId);
    }

    /**
     * 批量删除父母信息
     *
     * @param parentIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysParentInfoByIds(List<Long> parentIds) {
        return sysParentInfoMapper.deleteSysParentInfoByIds(parentIds);
    }

    /**
     * 根据用户ID删除父母信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteSysParentInfoByUserId(Long userId) {
        return sysParentInfoMapper.deleteSysParentInfoByUserId(userId);
    }
} 