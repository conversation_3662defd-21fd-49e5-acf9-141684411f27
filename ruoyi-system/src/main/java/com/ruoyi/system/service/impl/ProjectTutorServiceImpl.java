package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.ProjectTutor;
import com.ruoyi.system.mapper.ProjectTutorMapper;
import com.ruoyi.system.service.IProjectTutorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 项目辅导老师信息表(ProjectTutor)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
@Service
public class ProjectTutorServiceImpl implements IProjectTutorService {
    @Autowired
    private ProjectTutorMapper projectTutorMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ProjectTutor selectById(Integer id) {
        return this.projectTutorMapper.selectById(id);
    }

    /**
     * 通过项目ID查询辅导老师列表
     *
     * @param projectId 项目ID
     * @return 辅导老师列表
     */
    @Override
    public List<ProjectTutor> selectByProjectId(Integer projectId) {
        return this.projectTutorMapper.selectByProjectId(projectId);
    }

    /**
     * 查询指定条件的数据
     *
     * @param projectTutor 查询条件
     * @return 对象列表
     */
    @Override
    public List<ProjectTutor> selectList(ProjectTutor projectTutor) {
        return this.projectTutorMapper.selectList(projectTutor);
    }

    /**
     * 新增数据
     *
     * @param projectTutor 实例对象
     * @return 实例对象
     */
    @Override
    public ProjectTutor insert(ProjectTutor projectTutor) {
        this.projectTutorMapper.insert(projectTutor);
        return projectTutor;
    }

    /**
     * 批量新增数据
     *
     * @param projectTutors 实例对象列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsert(List<ProjectTutor> projectTutors) {
        return this.projectTutorMapper.batchInsert(projectTutors);
    }

    /**
     * 修改数据
     *
     * @param projectTutor 实例对象
     * @return 实例对象
     */
    @Override
    public ProjectTutor update(ProjectTutor projectTutor) {
        this.projectTutorMapper.update(projectTutor);
        return this.selectById(projectTutor.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.projectTutorMapper.deleteById(id) > 0;
    }

    /**
     * 通过项目ID删除数据
     *
     * @param projectId 项目ID
     * @return 是否成功
     */
    @Override
    public boolean deleteByProjectId(Integer projectId) {
        return this.projectTutorMapper.deleteByProjectId(projectId) > 0;
    }
} 