<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysTeamMemberMapper">

    <resultMap type="SysTeamMember" id="SysTeamMemberResult">
        <result property="memberId" column="member_id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="idNumber" column="ID_number"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="email" column="email"/>
        <result property="guardian" column="guardian"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="school" column="school"/>
        <result property="grade" column="grade"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="avatar" column="avatar"/>
        <result property="sex" column="sex"/>
        <result property="nationality" column="nationality"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="mailingAddress" column="mailing_address"/>
        <result property="schoolAddress" column="school_address"/>
        <result property="fatherName" column="father_name"/>
        <result property="fatherPhone" column="father_phone"/>
        <result property="fatherWorkplace" column="father_workplace"/>
        <result property="motherName" column="mother_name"/>
        <result property="motherPhone" column="mother_phone"/>
        <result property="motherWorkplace" column="mother_workplace"/>
        <result property="gradeText" column="grade_text"/>
        <result property="schoolDistrict" column="school_district"/>
        <result property="schoolDistrictText" column="school_district_text"/>
    </resultMap>

    <sql id="selectSysTeamMemberVo">
        select member_id, user_id, name, ID_number, phonenumber, email, guardian, guardian_phone, school, grade, create_by, create_time, update_by, update_time, avatar,
               sex, nationality, date_of_birth, mailing_address, school_address, father_name, father_phone, father_workplace, mother_name, mother_phone,
               mother_workplace, grade_text, school_district, school_district_text
        from sys_team_member
    </sql>

    <select id="selectSysTeamMemberList" parameterType="SysTeamMember" resultMap="SysTeamMemberResult">
        <include refid="selectSysTeamMemberVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="idNumber != null  and idNumber != ''"> and ID_number = #{idNumber}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="school != null  and school != ''"> and school like concat('%', #{school}, '%')</if>
            <if test="grade != null  and grade != ''"> and grade = #{grade}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="nationality != null  and nationality != ''"> and nationality = #{nationality}</if>
            <if test="dateOfBirth != null  and dateOfBirth != ''"> and date_of_birth = #{dateOfBirth}</if>
            <if test="mailingAddress != null  and mailingAddress != ''"> and mailing_address like concat('%', #{mailingAddress}, '%')</if>
            <if test="schoolAddress != null  and schoolAddress != ''"> and school_address like concat('%', #{schoolAddress}, '%')</if>
        </where>
    </select>

    <select id="selectTeamMembersByUserId" parameterType="Long" resultMap="SysTeamMemberResult">
        <include refid="selectSysTeamMemberVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertSysTeamMember" parameterType="SysTeamMember" useGeneratedKeys="true" keyProperty="memberId">
        insert into sys_team_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="idNumber != null">ID_number,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="email != null">email,</if>
            <if test="guardian != null">guardian,</if>
            <if test="guardianPhone != null">guardian_phone,</if>
            <if test="school != null">school,</if>
            <if test="grade != null">grade,</if>
            <if test="createBy != null">create_by,</if>
            <if test="avatar != null">avatar,</if>
            <if test="sex != null">sex,</if>
            <if test="nationality != null">nationality,</if>
            <if test="dateOfBirth != null">date_of_birth,</if>
            <if test="mailingAddress != null">mailing_address,</if>
            <if test="schoolAddress != null">school_address,</if>
            <if test="fatherName != null">father_name,</if>
            <if test="fatherPhone != null">father_phone,</if>
            <if test="fatherWorkplace != null">father_workplace,</if>
            <if test="motherName != null">mother_name,</if>
            <if test="motherPhone != null">mother_phone,</if>
            <if test="motherWorkplace != null">mother_workplace,</if>
            <if test="gradeText != null">grade_text,</if>
            <if test="schoolDistrict != null">school_district,</if>
            <if test="schoolDistrictText != null">school_district_text,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="email != null">#{email},</if>
            <if test="guardian != null">#{guardian},</if>
            <if test="guardianPhone != null">#{guardianPhone},</if>
            <if test="school != null">#{school},</if>
            <if test="grade != null">#{grade},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="sex != null">#{sex},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="dateOfBirth != null">#{dateOfBirth},</if>
            <if test="mailingAddress != null">#{mailingAddress},</if>
            <if test="schoolAddress != null">#{schoolAddress},</if>
            <if test="fatherName != null">#{fatherName},</if>
            <if test="fatherPhone != null">#{fatherPhone},</if>
            <if test="fatherWorkplace != null">#{fatherWorkplace},</if>
            <if test="motherName != null">#{motherName},</if>
            <if test="motherPhone != null">#{motherPhone},</if>
            <if test="motherWorkplace != null">#{motherWorkplace},</if>
            <if test="gradeText != null">#{gradeText},</if>
            <if test="schoolDistrict != null">#{schoolDistrict},</if>
            <if test="schoolDistrictText != null">#{schoolDistrictText},</if>
            sysdate()
        </trim>
    </insert>

    <insert id="batchInsertTeamMember" parameterType="java.util.List">
        insert into sys_team_member (
        user_id,
        name,
        ID_number,
        phonenumber,
        email,
        guardian,
        guardian_phone,
        school,
        grade,
        create_by,
        create_time,
        avatar,
        sex,
        nationality,
        date_of_birth,
        mailing_address,
        school_address,
        father_name,
        father_phone,
        father_workplace,
        mother_name,
        mother_phone,
        mother_workplace,
        grade_text,
        school_district,
        school_district_text
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
            (
            #{item.userId},
            #{item.name},
            #{item.idNumber},
            #{item.phonenumber},
            #{item.email},
            #{item.guardian},
            #{item.guardianPhone},
            #{item.school},
            #{item.grade},
            #{item.createBy},
            #{item.createTime},
            #{item.avatar},
            #{item.sex},
            #{item.nationality},
            #{item.dateOfBirth},
            #{item.mailingAddress},
            #{item.schoolAddress},
            #{item.fatherName},
            #{item.fatherPhone},
            #{item.fatherWorkplace},
            #{item.motherName},
            #{item.motherPhone},
            #{item.motherWorkplace},
            #{item.gradeText},
            #{item.schoolDistrict},
            #{item.schoolDistrictText}
            )
        </foreach>
    </insert>

    <update id="updateSysTeamMember" parameterType="SysTeamMember">
        update sys_team_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="idNumber != null">ID_number = #{idNumber},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="email != null">email = #{email},</if>
            <if test="guardian != null">guardian = #{guardian},</if>
            <if test="guardianPhone != null">guardian_phone = #{guardianPhone},</if>
            <if test="school != null">school = #{school},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            <if test="dateOfBirth != null">date_of_birth = #{dateOfBirth},</if>
            <if test="mailingAddress != null">mailing_address = #{mailingAddress},</if>
            <if test="schoolAddress != null">school_address = #{schoolAddress},</if>
            <if test="fatherName != null">father_name = #{fatherName},</if>
            <if test="fatherPhone != null">father_phone = #{fatherPhone},</if>
            <if test="fatherWorkplace != null">father_workplace = #{fatherWorkplace},</if>
            <if test="motherName != null">mother_name = #{motherName},</if>
            <if test="motherPhone != null">mother_phone = #{motherPhone},</if>
            <if test="motherWorkplace != null">mother_workplace = #{motherWorkplace},</if>
            <if test="gradeText != null">grade_text = #{gradeText},</if>
            <if test="schoolDistrict != null">school_district = #{schoolDistrict},</if>
            <if test="schoolDistrictText != null">school_district_text = #{schoolDistrictText},</if>
            update_time = sysdate()
        </trim>
        where member_id = #{memberId}
    </update>

    <delete id="deleteSysTeamMemberById" parameterType="Long">
        delete from sys_team_member where member_id = #{memberId}
    </delete>

    <delete id="deleteSysTeamMemberByIds" parameterType="String">
        delete from sys_team_member where member_id in
        <foreach item="memberId" collection="list" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>

    <delete id="deleteSysTeamMemberByUserId" parameterType="Long">
        delete from sys_team_member where user_id = #{userId}
    </delete>

    <select id="selectTeamMembersByCondition" parameterType="SysTeamMember" resultMap="SysTeamMemberResult">
        <include refid="selectSysTeamMemberVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="idNumber != null and idNumber != ''">
                AND ID_number = #{idNumber}
            </if>
            <if test="phonenumber != null and phonenumber != ''">
                AND phonenumber = #{phonenumber}
            </if>
            <if test="email != null and email != ''">
                AND email = #{email}
            </if>
            <if test="guardian != null and guardian != ''">
                AND guardian = #{guardian}
            </if>
            <if test="school != null and school != ''">
                AND school LIKE CONCAT('%', #{school}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectByCondition" parameterType="map" resultType="com.ruoyi.common.core.domain.entity.SysTeamMember">
        SELECT *
        FROM sys_team_member
        WHERE id_number = #{idNumber}
          AND user_id != #{userId}
    </select>

</mapper>
