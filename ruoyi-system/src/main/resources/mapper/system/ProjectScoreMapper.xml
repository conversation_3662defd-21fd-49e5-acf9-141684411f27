<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProjectScoreMapper">

    <resultMap type="com.ruoyi.system.domain.ProjectScore" id="ProjectScoreMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="score1" column="score1" jdbcType="DECIMAL"/>
        <result property="comment1" column="comment1" jdbcType="VARCHAR"/>
        <result property="judge1Id" column="judge1_id" jdbcType="BIGINT"/>
        <result property="scoreTime1" column="score_time1" jdbcType="TIMESTAMP"/>
        <result property="score2" column="score2" jdbcType="DECIMAL"/>
        <result property="comment2" column="comment2" jdbcType="VARCHAR"/>
        <result property="judge2Id" column="judge2_id" jdbcType="BIGINT"/>
        <result property="scoreTime2" column="score_time2" jdbcType="TIMESTAMP"/>
        <result property="score3" column="score3" jdbcType="DECIMAL"/>
        <result property="comment3" column="comment3" jdbcType="VARCHAR"/>
        <result property="judge3Id" column="judge3_id" jdbcType="BIGINT"/>
        <result property="scoreTime3" column="score_time3" jdbcType="TIMESTAMP"/>
        <result property="averageScore" column="average_score" jdbcType="DECIMAL"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--通过项目ID查询评分记录-->
    <select id="selectByProjectId" resultMap="ProjectScoreMap">
        SELECT
            id, project_id, score1, comment1, judge1_id, score_time1,
            score2, comment2, judge2_id, score_time2,
            score3, comment3, judge3_id, score_time3,
            average_score, created_time, updated_time, is_deleted
        FROM project_score
        WHERE project_id = #{projectId} AND is_deleted = 0
    </select>

    <!--新增评分记录-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO project_score(
            project_id, score1, comment1, judge1_id, score_time1,
            score2, comment2, judge2_id, score_time2,
            score3, comment3, judge3_id, score_time3,
            average_score, created_time, updated_time, is_deleted
        ) VALUES (
            #{projectId}, #{score1}, #{comment1}, #{judge1Id}, #{scoreTime1},
            #{score2}, #{comment2}, #{judge2Id}, #{scoreTime2},
            #{score3}, #{comment3}, #{judge3Id}, #{scoreTime3},
            #{averageScore}, #{createdTime}, #{updatedTime}, #{isDeleted}
        )
    </insert>

    <!--更新评分记录-->
    <update id="update">
        UPDATE project_score
        <set>
            <if test="score1 != null">score1 = #{score1},</if>
            <if test="comment1 != null">comment1 = #{comment1},</if>
            <if test="judge1Id != null">judge1_id = #{judge1Id},</if>
            <if test="scoreTime1 != null">score_time1 = #{scoreTime1},</if>
            <if test="score2 != null">score2 = #{score2},</if>
            <if test="comment2 != null">comment2 = #{comment2},</if>
            <if test="judge2Id != null">judge2_id = #{judge2Id},</if>
            <if test="scoreTime2 != null">score_time2 = #{scoreTime2},</if>
            <if test="score3 != null">score3 = #{score3},</if>
            <if test="comment3 != null">comment3 = #{comment3},</if>
            <if test="judge3Id != null">judge3_id = #{judge3Id},</if>
            <if test="scoreTime3 != null">score_time3 = #{scoreTime3},</if>
            <if test="averageScore != null">average_score = #{averageScore},</if>
            updated_time = #{updatedTime}
        </set>
        WHERE id = #{id}
    </update>

    <!--通过主键删除数据-->
    <delete id="deleteById">
        UPDATE project_score SET is_deleted = 1 WHERE id = #{id}
    </delete>

</mapper>
