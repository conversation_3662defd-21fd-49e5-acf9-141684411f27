<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysParentInfoMapper">

    <resultMap type="SysParentInfo" id="SysParentInfoResult">
        <result property="parentId" column="parent_id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="workplace" column="workplace"/>
        <result property="relationType" column="relation_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSysParentInfoVo">
        select parent_id, user_id, name, phone, workplace, relation_type, create_by, create_time, update_by, update_time
        from sys_parent_info
    </sql>

    <select id="selectSysParentInfoList" parameterType="SysParentInfo" resultMap="SysParentInfoResult">
        <include refid="selectSysParentInfoVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="workplace != null  and workplace != ''"> and workplace like concat('%', #{workplace}, '%')</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
        </where>
    </select>

    <select id="selectParentInfosByUserId" parameterType="Long" resultMap="SysParentInfoResult">
        <include refid="selectSysParentInfoVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertSysParentInfo" parameterType="SysParentInfo" useGeneratedKeys="true" keyProperty="parentId">
        insert into sys_parent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="workplace != null">workplace,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="workplace != null">#{workplace},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <insert id="batchInsertParentInfo" parameterType="java.util.List">
        insert into sys_parent_info (
        user_id,
        name,
        phone,
        workplace,
        relation_type,
        create_by,
        create_time
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
            (
            #{item.userId},
            #{item.name},
            #{item.phone},
            #{item.workplace},
            #{item.relationType},
            #{item.createBy},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="updateSysParentInfo" parameterType="SysParentInfo">
        update sys_parent_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="workplace != null">workplace = #{workplace},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where parent_id = #{parentId}
    </update>

    <delete id="deleteSysParentInfoById" parameterType="Long">
        delete from sys_parent_info where parent_id = #{parentId}
    </delete>

    <delete id="deleteSysParentInfoByIds" parameterType="String">
        delete from sys_parent_info where parent_id in
        <foreach item="parentId" collection="list" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </delete>

    <delete id="deleteSysParentInfoByUserId" parameterType="Long">
        delete from sys_parent_info where user_id = #{userId}
    </delete>

</mapper> 