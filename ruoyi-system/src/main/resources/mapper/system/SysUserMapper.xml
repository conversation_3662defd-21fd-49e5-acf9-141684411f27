<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="com.ruoyi.common.core.domain.entity.SysUser" id="SysUserResult">
        <id     property="userId"        column="user_id"         />
        <result property="deptId"        column="dept_id"         />
        <result property="userName"      column="user_name"       />
        <result property="nickName"      column="nick_name"       />
        <result property="email"         column="email"           />
        <result property="phonenumber"   column="phonenumber"     />
        <result property="sex"           column="sex"             />
        <result property="avatar"        column="avatar"          />
        <result property="password"      column="password"        />
        <result property="status"        column="status"          />
        <result property="delFlag"       column="del_flag"        />
        <result property="loginIp"       column="login_ip"        />
        <result property="loginDate"     column="login_date"      />
        <result property="pwdUpdateDate" column="pwd_update_date" />
        <result property="createBy"     column="create_by"        />
        <result property="createTime"   column="create_time"      />
        <result property="updateBy"     column="update_by"        />
        <result property="updateTime"   column="update_time"      />
        <result property="remark"       column="remark"           />
		<result property="dateOfBirth"       column="date_of_birth"           />
		<result property="IDNumber"       column="ID_number"           />
		<result property="school"       column="school"           />
		<result property="grade"       column="grade"           />
		<result property="schoolAddress"       column="school_address"           />
		<result property="schoolPostalCode"       column="school_postal_code"           />
		<result property="schoolPhone"       column="school_phone"           />
		<result property="schoolManager"       column="school_manager"           />
		<result property="guardian"       column="guardian"           />
		<result property="guardianPhone"       column="guardian_phone"           />
		<result property="mailingAddress"       column="mailing_address"           />
		<result property="mailingPostalCode"       column="mailing_postal_code"           />
		<result property="college"       column="college"           />
		<result property="fileUrl"       column="file_url"           />
		<result property="nationality"       column="nationality"           />
		<result property="schoolDistrict"       column="school_district"           />
		<result property="schoolDistrictText"       column="school_district_text"           />
        <association property="dept"    javaType="com.ruoyi.common.core.domain.entity.SysDept"         resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>

    <resultMap id="deptResult" type="com.ruoyi.common.core.domain.entity.SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>

    <resultMap id="RoleResult" type="com.ruoyi.common.core.domain.entity.SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
    </resultMap>

	<sql id="selectUserVo">
        select u.*,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="com.ruoyi.common.core.domain.entity.SysUser" resultMap="SysUserResult">
		select u.* ,d.dept_name, d.leader from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectAllocatedList" parameterType="com.ruoyi.common.core.domain.entity.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUnallocatedList" parameterType="com.ruoyi.common.core.domain.entity.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUserByUserName"  resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>

	<select id="selectUserById" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="checkUserNameUnique"  resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>

	<select id="checkPhoneUnique"  resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>

	<select id="checkEmailUnique"  resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>

	<insert id="insertUser" keyProperty="userId" useGeneratedKeys="true">
		INSERT INTO sys_user (
			dept_id,
			user_name,
			nick_name,
			user_type,
			email,
			phonenumber,
			sex,
			avatar,
			password,
			status,
			del_flag,
			login_ip,
			login_date,
			pwd_update_date,
			create_by,
			create_time,
			update_by,
			update_time,
			remark,
			nationality,
			date_of_birth,
			ID_number,
			school,
			grade,
			school_address,
			school_postal_code,
			school_phone,
			school_manager,
			guardian,
			guardian_phone,
			mailing_address,
			mailing_postal_code,
			college,
			file_url,
			school_district,
			school_district_text
		) VALUES (
					 #{deptId},
					 #{userName},
					 #{nickName},
					 #{userType},
					 #{email},
					 #{phonenumber},
					 #{sex},
					 #{avatar},
					 #{password},
					 #{status},
					 #{delFlag},
					 #{loginIp},
					 #{loginDate},
					 #{pwdUpdateDate},
					 #{createBy},
					 #{createTime},
					 #{updateBy},
					 #{updateTime},
					 #{remark},
					 #{nationality},
					 #{dateOfBirth},
					 #{IDNumber},
					 #{school},
					 #{grade},
					 #{schoolAddress},
					 #{schoolPostalCode},
					 #{schoolPhone},
					 #{schoolManager},
					 #{guardian},
					 #{guardianPhone},
					 #{mailingAddress},
					 #{mailingPostalCode},
					 #{college},
		             #{fileUrl},
		             #{schoolDistrict},
		             #{schoolDistrictText}
				 )
	</insert>

	<update id="updateUser" parameterType="com.ruoyi.common.core.domain.entity.SysUser">
		UPDATE sys_user
		<set>
			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="email != null ">email = #{email},</if>
			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
			<if test="sex != null and sex != ''">sex = #{sex},</if>
			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
			<if test="password != null and password != ''">password = #{password},</if>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
			<if test="loginDate != null">login_date = #{loginDate},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="remark != null">remark = #{remark},</if>
			<if test="nationality != null">nationality = #{nationality},</if>
			<if test="dateOfBirth != null">date_of_birth = #{dateOfBirth},</if>
			<if test="IDNumber != null">ID_number = #{IDNumber},</if>
			<if test="school != null">school = #{school},</if>
			<if test="grade != null">grade = #{grade},</if>
			<if test="schoolAddress != null">school_address = #{schoolAddress},</if>
			<if test="schoolPostalCode != null">school_postal_code = #{schoolPostalCode},</if>
			<if test="schoolPhone != null">school_phone = #{schoolPhone},</if>
			<if test="schoolManager != null">school_manager = #{schoolManager},</if>
			<if test="guardian != null">guardian = #{guardian},</if>
			<if test="guardianPhone != null">guardian_phone = #{guardianPhone},</if>
			<if test="mailingAddress != null">mailing_address = #{mailingAddress},</if>
			<if test="mailingPostalCode != null">mailing_postal_code = #{mailingPostalCode},</if>
			<if test="college != null">college = #{college},</if>
			<if test="fileUrl != null">file_url = #{fileUrl},</if>
		    <if test="schoolDistrict != null">school_district = #{schoolDistrict},</if>
		    <if test="schoolDistrictText != null">school_district_text = #{schoolDistrictText},</if>
			update_time = sysdate()
		</set>
		WHERE user_id = #{userId}
	</update>


	<update id="updateUserAvatar" parameterType="com.ruoyi.common.core.domain.entity.SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

	<update id="resetUserPwd" parameterType="com.ruoyi.common.core.domain.entity.SysUser">
 		update sys_user set pwd_update_date = sysdate(), password = #{password} where user_name = #{userName}
	</update>

	<delete id="deleteUserById" >
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>

 	<delete id="deleteUserByIds" >
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>

</mapper>
