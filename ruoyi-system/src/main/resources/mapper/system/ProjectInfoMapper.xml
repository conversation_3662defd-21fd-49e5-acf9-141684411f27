<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProjectInfoMapper">

    <resultMap type="com.ruoyi.system.domain.ProjectInfo" id="ProjectInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="district" column="district" jdbcType="VARCHAR"/>
        <result property="tutorName" column="tutor_name" jdbcType="VARCHAR"/>
        <result property="tutorContact" column="tutor_contact" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="creationFile" column="creation_file" jdbcType="VARCHAR"/>
        <result property="participantType" column="participant_type" jdbcType="VARCHAR"/>
        <result property="participantCount" column="participant_count" jdbcType="INTEGER"/>
        <result property="creatorStatement" column="creator_statement" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="workUrl" column="work_url" jdbcType="VARCHAR"/>
        <result property="addressContent" column="address_content" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="DOUBLE"/>
        <result property="scorer" column="scorer" jdbcType="VARCHAR"/>
        <result property="scoreTime" column="score_time" jdbcType="TIMESTAMP"/>
        <result property="averageScore" column="average_score" jdbcType="DECIMAL"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="declarationUrl" column="declaration_url" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
        <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
        <result property="descriptionText" column="description_text" jdbcType="VARCHAR"/>
        <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
        <result property="articleStatus" column="article_status" jdbcType="INTEGER"/>
        <result property="districtAuditStatus" column="district_audit_status" jdbcType="INTEGER"/>
        <result property="districtAuditRemark" column="district_audit_remark" jdbcType="VARCHAR"/>
        <result property="districtAuditor" column="district_auditor" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ProjectInfoMap">
        SELECT
            id,
            CASE
                WHEN category = '1' THEN 'AI艺术生成'
                WHEN category = '2' THEN 'AI交互设计'
                WHEN category = '3' THEN 'AI工程实践'
                WHEN category = '4' THEN 'AI算法挑战'
                WHEN category = '5' THEN 'AI创新教学案例征集'
                ELSE '未知类别'
                END AS category,
            name,
            district,
            tutor_name,
            tutor_contact,
            description,
            creation_file,
            participant_type,
            participant_count,
            creator_statement,
            created_time,
            updated_time,
            is_deleted,
            work_url,
            address_content,
            score,
            scorer,
            score_time,
            average_score,
            user_name,
            declaration_url,
            audit_status,
            audit_remark,
            description_text,
            auditor,
            article_status,
            district_audit_status,
            district_audit_remark,
            district_auditor
        FROM
            project_info
        where id = #{id}
    </select>

    <select id="queryAllByLimit" resultMap="ProjectInfoMap">
        SELECT
        id,
        CASE
        WHEN category = '1' THEN 'AI艺术生成'
        WHEN category = '2' THEN 'AI交互设计'
        WHEN category = '3' THEN 'AI工程实践'
        WHEN category = '4' THEN 'AI算法挑战'
        WHEN category = '5' THEN 'AI创新教学案例征集'
        ELSE '未知类别'
        END AS category,
        name,
        district,
        tutor_name,
        tutor_contact,
        description,
        creation_file,
        participant_type,
        participant_count,
        creator_statement,
        created_time,
        updated_time,
        is_deleted,
        work_url,
        address_content,
        score,
        scorer,
        score_time,
        average_score,
        user_name,
        declaration_url,
        audit_status,
        audit_remark,
        description_text,
        auditor,
        article_status,
        district_audit_status,
        district_audit_remark,
        district_auditor
        FROM
        project_info
        <where>
           1=1
           and is_deleted = 0
            <if test="category != null and category != ''">
                and  category = #{category}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="userName != null and userName != ''">
                and  user_name = #{userName}
            </if>
            <if test="district != null and district != ''">
                and  district = #{district}
            </if>
            <if test="participantType != null and participantType != ''">
                and  participant_type = #{participantType}
            </if>
            <if test="articleStatus != null">
                and  article_status = #{articleStatus}
            </if>
        </where>
        ORDER BY updated_time DESC
    </select>

    <select id="queryAllUserNameByLimit" resultType="java.lang.String">
        SELECT
        user_name
        FROM
        project_info
        <where>
            1=1
            and is_deleted = 0
            <if test="category != null and category != ''">
                and  category = #{category}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="userName != null and userName != ''">
                and  user_name = #{userName}
            </if>
            <if test="district != null and district != ''">
                and  district = #{district}
            </if>
            <if test="participantType != null and participantType != ''">
                and  participant_type = #{participantType}
            </if>
            <if test="articleStatus != null">
                and  article_status = #{articleStatus}
            </if>
        </where>
    </select>
    <select id="queryAllUserIdByLimit" resultType="java.lang.Long">
        SELECT
        user_id
        FROM
        project_info
        <where>
            1=1
            and is_deleted = 0
            <if test="category != null and category != ''">
                and  category = #{category}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="userName != null and userName != ''">
                and  user_name = #{userName}
            </if>
            <if test="district != null and district != ''">
                and  district = #{district}
            </if>
            <if test="participantType != null and participantType != ''">
                and  participant_type = #{participantType}
            </if>
            <if test="articleStatus != null">
                and  article_status = #{articleStatus}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from project_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="district != null and district != ''">
                and district = #{district}
            </if>
            <if test="tutorName != null and tutorName != ''">
                and tutor_name = #{tutorName}
            </if>
            <if test="tutorContact != null and tutorContact != ''">
                and tutor_contact = #{tutorContact}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="creationFile != null and creationFile != ''">
                and creation_file = #{creationFile}
            </if>
            <if test="participantType != null and participantType != ''">
                and participant_type = #{participantType}
            </if>
            <if test="participantCount != null">
                and participant_count = #{participantCount}
            </if>
            <if test="creatorStatement != null and creatorStatement != ''">
                and creator_statement = #{creatorStatement}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updatedTime != null">
                and updated_time = #{updatedTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into project_info(category,name,district,tutor_name,tutor_contact,description,creation_file,participant_type,participant_count,creator_statement,created_time,updated_time,is_deleted,work_url,address_content,user_name,declaration_url,
                                 audit_status,audit_remark, description_text, auditor, article_status, district_audit_status, district_audit_remark, district_auditor)
        values (#{category},#{name},#{district},#{tutorName},#{tutorContact},#{description},#{creationFile},#{participantType},#{participantCount},#{creatorStatement},#{createdTime},#{updatedTime},#{isDeleted},#{workUrl},#{addressContent}, #{userName}, #{declarationUrl},
                                    #{auditStatus}, #{auditRemark}, #{descriptionText}, #{auditor}, #{articleStatus}, #{districtAuditStatus}, #{districtAuditRemark}, #{districtAuditor})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update project_info
        <set>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="district != null and district != ''">
                district = #{district},
            </if>
            <if test="tutorName != null and tutorName != ''">
                tutor_name = #{tutorName},
            </if>
            <if test="tutorContact != null and tutorContact != ''">
                tutor_contact = #{tutorContact},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="creationFile != null and creationFile != ''">
                creation_file = #{creationFile},
            </if>
            <if test="participantType != null and participantType != ''">
                participant_type = #{participantType},
            </if>
            <if test="participantCount != null">
                participant_count = #{participantCount},
            </if>
            <if test="creatorStatement != null and creatorStatement != ''">
                creator_statement = #{creatorStatement},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>

            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="workUrl != null">
                work_url = #{workUrl},
            </if>
            <if test="score != null ">
                score = #{score},
            </if>
            <if test="scorer != null">
                scorer = #{scorer},
            </if>
            <if test="scoreTime != null">
                score_time = #{scoreTime},
            </if>
            <if test="averageScore != null">
                average_score = #{averageScore},
            </if>
            <if test="addressContent != null">
                address_content = #{addressContent},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="declarationUrl != null and declarationUrl != ''">
                declaration_url = #{declarationUrl},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus},
            </if>
            <if test="auditRemark != null">
                audit_remark = #{auditRemark},
            </if>
            <if test="descriptionText != null and descriptionText != ''">
                description_text = #{descriptionText},
            </if>
            <if test="auditor != null and auditor != ''">
                auditor = #{auditor},
            </if>
            <if test="articleStatus != null">
                article_status = #{articleStatus},
            </if>
            <if test="districtAuditStatus != null">
                district_audit_status = #{districtAuditStatus},
            </if>
            <if test="districtAuditRemark != null">
                district_audit_remark = #{districtAuditRemark},
            </if>
            <if test="districtAuditor != null and districtAuditor != ''">
                district_auditor = #{districtAuditor},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        update
         project_info
        set
         is_deleted = 1
        where id = #{id}
    </delete>

    <update id="updateDeclaration">
        update project_info
        <set>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="declarationUrl != null and declarationUrl != ''">
                declaration_url = #{declarationUrl},
            </if>
            <if test="articleStatus != null">
                article_status = #{articleStatus},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus},
            </if>
            <if test="districtAuditStatus != null">
                district_audit_status = #{districtAuditStatus},
            </if>

        </set>
        where id = #{id}
    </update>

    <update id="updateAudit">
        update project_info
        <set>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus},
            </if>
            <if test="auditRemark != null">
                audit_remark = #{auditRemark},
            </if>
            <if test="auditor != null and auditor != ''">
                auditor = #{auditor},
            </if>
            <if test="articleStatus != null">
                article_status = #{articleStatus},
            </if>
            <if test="districtAuditStatus != null">
                district_audit_status = #{districtAuditStatus},
            </if>
            <if test="districtAuditRemark != null and districtAuditRemark != ''">
                district_audit_remark = #{districtAuditRemark},
            </if>
            <if test="districtAuditor != null and districtAuditor != ''">
                district_auditor = #{districtAuditor},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--更新项目平均分-->
    <update id="updateAverageScore">
        UPDATE project_info
        SET average_score = #{averageScore}
        WHERE id = #{projectId}
    </update>

</mapper>
