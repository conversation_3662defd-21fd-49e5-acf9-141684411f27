<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProjectTutorMapper">

    <resultMap type="com.ruoyi.system.domain.ProjectTutor" id="ProjectTutorMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="contact" column="contact" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="workUnit" column="work_unit" jdbcType="VARCHAR"/>
        <result property="district" column="district" jdbcType="VARCHAR"/>
        <result property="districtText" column="district_text" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="selectProjectTutorVo">
        select id, project_id, name, gender, contact, email, title, work_unit, district, district_text, 
               created_time, updated_time, is_deleted, sort_order
        from project_tutor
    </sql>

    <!--查询单个-->
    <select id="selectById" resultMap="ProjectTutorMap">
        <include refid="selectProjectTutorVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <!--通过项目ID查询辅导老师列表-->
    <select id="selectByProjectId" resultMap="ProjectTutorMap">
        <include refid="selectProjectTutorVo"/>
        where project_id = #{projectId} and is_deleted = 0
        order by sort_order asc
    </select>

    <!--查询指定行数据-->
    <select id="selectList" resultMap="ProjectTutorMap">
        <include refid="selectProjectTutorVo"/>
        <where>
            is_deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="gender != null and gender != ''">
                and gender = #{gender}
            </if>
            <if test="contact != null and contact != ''">
                and contact = #{contact}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="workUnit != null and workUnit != ''">
                and work_unit like concat('%', #{workUnit}, '%')
            </if>
            <if test="district != null and district != ''">
                and district = #{district}
            </if>
            <if test="sortOrder != null">
                and sort_order = #{sortOrder}
            </if>
        </where>
        order by sort_order asc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into project_tutor(
            project_id, name, gender, contact, email, title, work_unit, district, district_text, 
            created_time, updated_time, is_deleted, sort_order
        ) values (
            #{projectId}, #{name}, #{gender}, #{contact}, #{email}, #{title}, #{workUnit}, #{district}, #{districtText}, 
            now(), now(), 0, #{sortOrder}
        )
    </insert>

    <!--批量新增-->
    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into project_tutor(
            project_id, name, gender, contact, email, title, work_unit, district, district_text, 
            created_time, updated_time, is_deleted, sort_order
        ) values 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.projectId}, #{item.name}, #{item.gender}, #{item.contact}, #{item.email}, #{item.title}, 
            #{item.workUnit}, #{item.district}, #{item.districtText}, now(), now(), 0, #{item.sortOrder}
            )
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update project_tutor
        <set>
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="contact != null and contact != ''">
                contact = #{contact},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="workUnit != null and workUnit != ''">
                work_unit = #{workUnit},
            </if>
            <if test="district != null and district != ''">
                district = #{district},
            </if>
            <if test="districtText != null and districtText != ''">
                district_text = #{districtText},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder},
            </if>
            updated_time = now()
        </set>
        where id = #{id} and is_deleted = 0
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        update project_tutor set is_deleted = 1, updated_time = now() where id = #{id}
    </delete>

    <!--通过项目ID删除-->
    <delete id="deleteByProjectId">
        update project_tutor set is_deleted = 1, updated_time = now() where project_id = #{projectId}
    </delete>

</mapper> 