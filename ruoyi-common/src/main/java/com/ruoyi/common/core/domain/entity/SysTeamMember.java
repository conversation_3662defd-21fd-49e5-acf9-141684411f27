package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户团队成员信息表 sys_team_member
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
// @TableName("sys_team_member")
public class SysTeamMember extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 团队成员ID */
    // @TableId(type = IdType.AUTO)
    private Long memberId;

    /** 关联的用户ID */
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String idNumber;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phonenumber;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 监护人 */
    @Excel(name = "监护人")
    private String guardian;

    /** 监护人电话 */
    @Excel(name = "监护人电话")
    private String guardianPhone;

    /** 学校名称 */
    @Excel(name = "学校名称")
    private String school;

    /** 学段 */
    @Excel(name = "学段")
    private String grade;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 性别（0男 1女 2未知） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 民族 */
    @Excel(name = "民族")
    private String nationality;

    /** 出生年月 */
    @Excel(name = "出生年月")
    private String dateOfBirth;

    /** 邮寄地址 */
    @Excel(name = "邮寄地址")
    private String mailingAddress;

    /** 学校地址 */
    @Excel(name = "学校地址")
    private String schoolAddress;

    /** 父亲姓名 */
    @Excel(name = "父亲姓名")
    private String fatherName;

    /** 父亲电话 */
    @Excel(name = "父亲电话")
    private String fatherPhone;

    /** 父亲工作单位 */
    @Excel(name = "父亲工作单位")
    private String fatherWorkplace;

    /** 母亲姓名 */
    @Excel(name = "母亲姓名")
    private String motherName;

    /** 母亲电话 */
    @Excel(name = "母亲电话")
    private String motherPhone;

    /** 母亲工作单位 */
    @Excel(name = "母亲工作单位")
    private String motherWorkplace;

    /** 年级 */
    @Excel(name = "年级")
    private String gradeText;

    /** 学校所在区编码 */
    @Excel(name = "学校所在区编码")
    private String schoolDistrict;

    /** 学校所在区 */
    @Excel(name = "学校所在区")
    private String schoolDistrictText;

}
