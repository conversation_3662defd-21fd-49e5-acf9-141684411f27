package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户父母信息表 sys_parent_info
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysParentInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 父母信息ID */
    private Long parentId;

    /** 关联的用户ID */
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String phone;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String workplace;

    /** 关系类型（0父亲 1母亲） */
    @Excel(name = "关系类型", readConverterExp = "0=父亲,1=母亲")
    private String relationType;
} 