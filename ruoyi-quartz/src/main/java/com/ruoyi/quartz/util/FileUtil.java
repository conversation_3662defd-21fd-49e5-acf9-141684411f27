package com.ruoyi.quartz.util;

import cn.hutool.core.io.IoUtil;
import com.ruoyi.common.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

@Slf4j
@Component
public class FileUtil {

    /**
     * 判断导入的文件是否是图片
     *
     * @param contentType 文件真实类型
     * @return 是 返回true ，否 返回 false
     */
    public static boolean isImage(String contentType) {
        if ("image/jpeg".equalsIgnoreCase(contentType)) {
            return true;
        }
        return "image/jpg".equalsIgnoreCase(contentType);
    }

    /**
     * 判断导入的文件是否是图片
     *
     * @param contentType 文件真实类型
     * @return 是 返回true ，否 返回 false
     */
    public static String getImgSuffix(String contentType) {
        if ("image/jpeg".equalsIgnoreCase(contentType)) {
            return "jpeg";
        } else if ("image/gif".equalsIgnoreCase(contentType)) {
            return "gif";
        } else if ("image/png".equalsIgnoreCase(contentType)) {
            return "png";
        } else if ("image/jpg".equalsIgnoreCase(contentType)) {
            return "jpg";
        } else if ("image/bmp".equalsIgnoreCase(contentType)) {
            return "bmp";
        } else if ("image/gif".equalsIgnoreCase(contentType)) {
            return "gif";
        } else if ("image/webp".equals(contentType)) {
            return "webp";
        } else {
            return "";
        }
    }

    /**
     * 判断文件目录是否存在，不存在则创建
     *
     * @param fileDir
     * @return
     */
    public static boolean createDirIfNotExists(String fileDir) {
        File file = new File(fileDir);
        if (file.exists()) {
            return true;
        }
        if (file.mkdirs()) {// 创建多级目录
            return true;
        } else {
            return false;
        }
    }

    /**
     * 将inputStream转化为file
     *
     * @param is
     * @param file 要输出的文件目录
     */
    public static void inputStream2File(InputStream is, File file) throws IOException {
        OutputStream os = null;
        try {
            os = new FileOutputStream(file);
            int len = 0;
            byte[] buffer = new byte[8192];

            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
        } finally {
            os.close();
            is.close();
        }
    }

    /**
     * 压缩图片
     *
     * @param pictureRoute
     * @return
     */
    public static void compressPicture(String pictureRoute) {
        File srcfile = new File(pictureRoute);
        // 检查图片文件是否存在
        if (!srcfile.exists()) {
            log.error("图片文件不存在");
            return;
        }
        long length = srcfile.length() / 1024;
        if (length <= 500) {
            log.info("头像图片小于500Kb，大小是{}kb，不做处理", length);
            return;
        }
        log.info("头像图片大于500kb并开始进行压缩，原始大小是{}kb", length);
        int widthdist = 480;
        int heightdist = 480;
        InputStream is;
        BufferedImage src;
        double result[] = {0, 0};
        try {
            // 获得文件输入流
            is = new FileInputStream(srcfile);
            // 从流里将图片写入缓冲图片区
            src = ImageIO.read(is);
            result[0] = src.getWidth(null); // 得到源图片宽
            result[1] = src.getHeight(null);// 得到源图片高
            is.close();  //关闭输入流
            heightdist = (int) (heightdist / (result[0] / result[1]));
            // 开始读取文件并进行压缩
            src = ImageIO.read(srcfile);
            // 构造一个类型为预定义图像类型之一的 BufferedImage
            BufferedImage tag = new BufferedImage(widthdist, heightdist, BufferedImage.TYPE_INT_RGB);
            //绘制图像  getScaledInstance表示创建此图像的缩放版本，返回一个新的缩放版本Image,按指定的width,height呈现图像
            //Image.SCALE_SMOOTH,选择图像平滑度比缩放速度具有更高优先级的图像缩放算法。
            tag.getGraphics().drawImage(src.getScaledInstance(widthdist, heightdist, Image.SCALE_SMOOTH), 0, 0, null);
            //创建文件输出流
            FileOutputStream out = new FileOutputStream(pictureRoute);
            ImageIO.write(tag, "jpg", out);
            //关闭文件输出流
            out.close();
            log.info("图片压缩完毕");
        } catch (Exception ef) {
            log.error("压缩图片发生异常", ef);
        }
    }

    /**
     * 下载文件
     *
     * @param path
     * @return
     */
    public static void downloadOnlinePDF(String path) {
        HttpServletResponse response = ServletUtils.getResponse();
        FileInputStream inputStream = null;
        ServletOutputStream outputStream = null;
        try {

            // path是指欲下载的文件的路径。
            File file = new File(path);
            // 取得文件名。
            String filename = file.getName();
            // 清空response
            response.reset();
            // 设置response的Header
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader("Content-Disposition", "inline; filename=" + new String(filename.getBytes()));
            response.addHeader("Content-Length", "" + file.length());
//            response.setContentType("application/octet-stream");
            inputStream = new FileInputStream(path);
            outputStream = response.getOutputStream();
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException e) {
            //抛出一个运行时异常(直接停止掉程序)
            throw new RuntimeException("运行时异常", e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        }
    }

    /**
     * 获取文件后缀，以实现图片的预览功能
     *
     * @param FilenameExtension
     * @return
     */
    public static String getContentType(String FilenameExtension) {
        if (FilenameExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        }
        if (FilenameExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        }
        if (FilenameExtension.equalsIgnoreCase(".jpeg") ||
                FilenameExtension.equalsIgnoreCase(".jpg") ||
                FilenameExtension.equalsIgnoreCase(".png")) {
            return "image/jpg";
        }
        if (FilenameExtension.equalsIgnoreCase(".html")) {
            return "text/html";
        }
        if (FilenameExtension.equalsIgnoreCase(".txt")) {
            return "text/plain";
        }
        if (FilenameExtension.equalsIgnoreCase(".vsd")) {
            return "application/vnd.visio";
        }
        if (FilenameExtension.equalsIgnoreCase(".pptx") ||
                FilenameExtension.equalsIgnoreCase(".ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (FilenameExtension.equalsIgnoreCase(".docx") ||
                FilenameExtension.equalsIgnoreCase(".doc")) {
            return "application/msword";
        }
        if (FilenameExtension.equalsIgnoreCase(".xml")) {
            return "text/xml";
        }
        return "image/jpg";
    }

}
