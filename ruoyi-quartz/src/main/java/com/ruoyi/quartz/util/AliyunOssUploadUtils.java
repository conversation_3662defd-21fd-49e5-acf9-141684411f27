package com.ruoyi.quartz.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.quartz.oss.AliyunOssProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Component
public class AliyunOssUploadUtils {
    @Autowired
    private AliyunOssProperties aliyunOssProperties;

    /**
     * 上传文件到阿里云，并生成 URL
     *
     * @param filename 文件名
     * @param in       文件字节流
     * @param contentLength 文件大小
     * @param contentType 文件类型
     * @return String 生成的文件 URL
     */
    public String uploadToAliyun(String filename, InputStream in, long contentLength, String contentType) {
        OSSClient ossClient = null;
        try {
            ossClient = new OSSClient(
                    aliyunOssProperties.getEndpoint(),
                    aliyunOssProperties.getAccessKeyId(),
                    aliyunOssProperties.getAccessKeySecret()
            );

            // 创建上传 Object 的 Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(contentLength);
            objectMetadata.setCacheControl("no-cache"); // 设置 Cache-Control 请求头
            objectMetadata.setHeader("Pragma", "no-cache"); // 设置页面不缓存
            objectMetadata.setContentType(contentType);
            objectMetadata.setContentDisposition("inline;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()));

            // 上传文件
            String fileKey = IdUtils.simpleUUID();
            if (filename.contains("__")) {
                fileKey = filename;
            }

            ossClient.putObject(aliyunOssProperties.getBucketName(), fileKey, in, objectMetadata);

            // URL 过期时间（十年）
            Date expiration = new Date(new Date().getTime() + 3600L * 1000 * 24 * 365 * 10);
            URL url = ossClient.generatePresignedUrl(aliyunOssProperties.getBucketName(), fileKey, expiration);

            return url.toString();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}