package com.ruoyi.quartz.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
public class DateUtil {

    public static final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static final SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd");
    public static final SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
    public static final SimpleDateFormat format3 = new SimpleDateFormat("HHmmss");
    public static final SimpleDateFormat format4 = new SimpleDateFormat("yyyyMMddHHmmss");
    public static final SimpleDateFormat sdfutc = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'+08:00'");
    public static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    /**
     * 时间戳为0的时间，即1970-01-01 08:00:00
     */
    private static Date date0 = new Date(0);

    /**
     * 获取时间戳为0的时间
     *
     * @return
     */
    public static Date getDate0() {
        return date0;
    }

    /**
     * 判断时间是否为null
     *
     * @param date
     * @return true 是null   false 不是null
     */
    public static boolean isNull(Date date) {
        if (date == date0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取时间起止
     *
     * @param date10 yyyy-MM-dd
     * @return 返回这天的时间起止
     * 2022-01-02 00:00:00
     * 2022-01-02 23:59:59
     * @throws Exception
     */
    public static Map<String, String> getTimeInterval(String date10) {
        Map<String, String> resultMap = new HashMap<>();
        String startTime = "";
        String endTime = "";
        if (!validDateStr(date10, "yyyy-MM-dd")) {
            throw new RuntimeException("时间格式不正确");
        }
        // 开始时间
        startTime = date10 + " 00:00:00";
        // 结束时间
        if (date10.equals(getNow10())) {
            // 当天
            endTime = getNow19();
        } else {
            // 其他天
            endTime = date10 + " 23:59:59";
        }

        resultMap.put("startTime", startTime);
        resultMap.put("endTime", endTime);
        return resultMap;
    }

    /**
     * 切割时间
     *
     * @param startTime yyyy-MM-dd HH:mm:ss
     * @param endTime   yyyy-MM-dd HH:mm:ss
     * @param interval  粒度（分钟）
     * @return
     * @throws Exception
     */
    public static List<Map<String, String>> cutTime(String startTime, String endTime, int interval) {

        List<Map<String, String>> resultList = new ArrayList<>();

        try {
            // 结果点数
            long distanceMin = getDistanceMin(startTime, endTime);
            long pointTotal = distanceMin / interval + 2;

            // 前后时间计算，并转换成秒
            int intervalHalf = interval * 60 / 2;

            Calendar cal = Calendar.getInstance();
            Date date0 = str19ToDate(startTime);
            // 设置起时间
            cal.setTime(date0);
            for (int i = 0; i < pointTotal; i++) {
                Map<String, String> tempMap = new HashMap<>();
                // 中间
                String midDate = dateToStr19(cal.getTime());
                tempMap.put("point", midDate);
                if (getDistanceMin(startTime, midDate) >= 24 * 60) {
                    // 如果超出到第二天，显示当天24:00
                    tempMap.put("point", startTime.substring(0, 11) + "24:00:00");
                }
                // 前
                Calendar calTempBefore = Calendar.getInstance();
                calTempBefore.setTime(cal.getTime());
                calTempBefore.add(Calendar.SECOND, -intervalHalf);
                tempMap.put("before", dateToStr19(calTempBefore.getTime()));
                // 后
                Calendar calTempAfter = Calendar.getInstance();
                calTempAfter.setTime(cal.getTime());
                calTempAfter.add(Calendar.SECOND, intervalHalf);
                tempMap.put("after", dateToStr19(calTempAfter.getTime()));
                resultList.add(tempMap);

                cal.add(Calendar.MINUTE, interval);
            }
        } catch (Exception e) {
            throw new RuntimeException("时间转换出现异常", e);
        }
        return resultList;
    }

    /**
     * 相差分钟数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static long getDistanceMin(String startTime, String endTime) {
        long l;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = sdf.parse(startTime);
            Date date2 = sdf.parse(endTime);
            l = date2.getTime() - date1.getTime();
        } catch (ParseException e) {
            throw new RuntimeException("日期转换异常");
        }
        return l / (60 * 1000);
    }

    /**
     * 相差秒数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static long getDistanceSec(String startTime, String endTime) {
        long l;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = sdf.parse(startTime);
            Date date2 = sdf.parse(endTime);
            l = date2.getTime() - date1.getTime();
        } catch (ParseException e) {
            throw new RuntimeException("日期转换异常");
        }
        return l / (1000);
    }

    /**
     * 时间间隔
     *
     * @param str1 小时间
     * @param str2 大时间
     * @return
     */
    public static String getSimpleDistanceTimes(String str1, String str2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date1 = null;
        Date date2 = null;
        try {
            date1 = sdf.parse(str1);
            date2 = sdf.parse(str2);
        } catch (ParseException e) {
            e.printStackTrace();
            throw new RuntimeException("日期转换异常");
        }
        long l = date2.getTime() - date1.getTime();
        long day = l / (24 * 60 * 60 * 1000);
        long hour = (l / (60 * 60 * 1000) - day * 24);
        long min = ((l / (60 * 1000)) - day * 24 * 60 - hour * 60);
        return hour + "小时" + min + "分";
    }

    /**
     * 校验是否合法
     *
     * @param dateStr
     * @param pattern
     * @return
     */
    public static boolean validDateStr(String dateStr, String pattern) {
        if (StringUtils.isBlank(dateStr)) {
            return false;
        }
        if (StringUtils.isEmpty(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        try {
            LocalDate.parse(dateStr, new DateTimeFormatterBuilder().appendPattern(pattern).parseStrict().toFormatter());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * long转date
     *
     * @param timestamp（单位：毫秒）
     * @return
     */
    public static Date longToDate(long timestamp) {
        return new Date(timestamp);
    }

    /**
     * date转long
     *
     * @param date
     * @return （单位：毫秒）
     */
    public static long dateToLong(Date date) {
        return date.getTime();
    }

    /**
     * 获取当天0点的时间戳
     *
     * @return
     */
    public static long getTodayZero() {
        Calendar calendar = new GregorianCalendar();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long timeInMillis = calendar.getTimeInMillis();
        return timeInMillis;
    }

    public static Map<String, String> getYesterdayInterval() {
        String d = dateCalculator(DateUtil.getNow10() + " 00:00:00", "d", -1, 1);
        return getTimeInterval(d.substring(0, 10));
    }

    /**
     * 日期加减
     *
     * @param dateStr 起始日期
     * @param ymdStr  选择y年、m月、d天
     * @param operate 1加 -1减
     * @param num     数量
     * @return
     */
    public static String dateCalculator(String dateStr, String ymdStr, Integer operate, Integer num) {
        Calendar cal = Calendar.getInstance();
        Date date = str19ToDate(dateStr);
        cal.setTime(date);//设置起时间
        int ymd = 0;
        switch (ymdStr) {
            case "y":
                ymd = Calendar.YEAR;
                break;
            case "m":
                ymd = Calendar.MONTH;
                break;
            case "d":
                ymd = Calendar.DATE;
                break;
            case "hh":
                ymd = Calendar.HOUR;
                break;
            case "mm":
                ymd = Calendar.MINUTE;
                break;
            case "ss":
                ymd = Calendar.SECOND;
                break;
            default:
                break;
        }
        int amount = operate * num;
        cal.add(ymd, amount);

        Date time = cal.getTime();
        return dateToStr19(time);
    }

    /**
     * 获取此时此刻的时间（String)
     *
     * @return
     */
    public static String getNow19() {
        return format.format(new Date());
    }

    /**
     * 获取此时此刻的时间格式1（String)
     *
     * @return
     */
    public static String getNowDate1String() {
        return format1.format(new Date());
    }

    /**
     * 获取此时此刻的时间格式2（String)
     *
     * @return
     */
    public static String getNow10() {
        return format2.format(new Date());
    }

    /**
     * 获取此时此刻的时间格式3（String)
     *
     * @return
     */
    public static String getNowDate3String() {
        return format3.format(new Date());
    }

    /**
     * 获取此时此刻的时间格式3（String)
     *
     * @return
     */
    public static String getNowDate4String() {
        return format4.format(new Date());
    }

    /**
     * 获取时间格式3（String)
     *
     * @return
     */
    public static String getDate3String(Date date) {
        return format3.format(date);
    }

    /**
     * 转换日期格式
     *
     * @param localDate 2021-09-30T16:00:00.000+00:00
     * @return 2021-09-30 16:00:00
     */
    public static String processDate(String localDate) {
        if (StringUtils.isNotBlank(localDate)) {
            LocalDateTime localDateTime = LocalDateTime.parse(localDate, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(localDateTime);
        }
        return null;
    }

    /**
     * 获取过去几天的日期的年月日，不含当天
     *
     * @param days
     * @return dayBefore
     */
    public static List<String> getSomeDaysAgoList(int days) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < days; i++) {
            Calendar c = Calendar.getInstance();
            Date date = new Date();
            c.setTime(date);
            c.add(Calendar.DATE, -(days - i));
            String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
            list.add(dayBefore);
        }
        return list;
    }

    /**
     * 获取过去的日期的年月日，不含当天
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getSomeDaysAgoInterval(String startTime, String endTime) {
        List<String> list = new ArrayList<>();
        int i = 0;
        String dayAdd = "";
        while (dayAdd.compareTo(endTime) < 0) {
            Calendar c = Calendar.getInstance();
            Date dateStart = DateUtil.str10ToDate(startTime);
            c.setTime(dateStart);
            c.add(Calendar.DATE, i);
            dayAdd = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
            list.add(dayAdd);
            i++;
        }
        return list;
    }

    /**
     * 得到从现在开始之前days的日期的年月日
     *
     * @param days
     * @return dayBefore
     */
    public static String getSomeDaysAgo(int days) {
        Calendar c = Calendar.getInstance();
        Date date = new Date();
        c.setTime(date);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day - days);
        String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime()) + " 00:00:00";
        return dayBefore;
    }

    public static String dateToStr(Date date) {
        String s = "";
        if (date == null) {
            return "";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        s = sdf.format(date);

        return s;
    }

    public static String dateToStr19(Date date) {
        String s = "";
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            s = sdf.format(date);
        } catch (Exception e) {
            throw new RuntimeException("日期转换异常");
        }
        return s;
    }

    public static Date str10ToDate(String s) {
        return strToDate(s, DateUtil.format2);
    }

    public static Date str19ToDate(String s) {
        // 在多线程下，SimpleDateFormat 对象极易发生错误，在此单独创建
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return strToDate(s, format);
    }

    public static Date strToDate(String s, SimpleDateFormat format) {
        Date d;
        try {
            d = format.parse(s);
        } catch (ParseException e) {
            throw new RuntimeException("日期转换异常");
        }
        return d;
    }

    /**
     * 普通时间 转 UTC
     *
     * @return
     */
    public static String normal2UTC(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        Date date;
        try {
            date = format.parse(str);
        } catch (ParseException e) {
            throw new RuntimeException("日期转换异常");
        }
        sdfutc.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdfutc.format(date);
    }


    /**
     * 获取指定的月份所有日期
     *
     * @param year  年
     * @param month 月
     * @return list
     */
    public static List<Date> getMonthDaysDate(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        month = month - 1;
        calendar.set(year, month, 0);
        return getAllDayDate(calendar, true);
    }


    private static List<Date> getAllDayDate(Calendar cal, boolean lengthFlag) {
        List<Date> list = new ArrayList<>();
        int maxDate = cal.getActualMaximum(Calendar.DATE);
        if (!lengthFlag) {
            maxDate = cal.get(Calendar.DAY_OF_MONTH);
        }
        cal.set(Calendar.DAY_OF_MONTH, 1);
        for (int i = 0; i < maxDate; i++, cal.add(Calendar.DATE, 1)) {
            list.add(cal.getTime());
        }
        return list;
    }

    /**
     * 获取当月第一天
     *
     * @param month
     * @return
     */
    public static String getFirstDayOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return dateToStr19(calendar.getTime());
    }

    /**
     * 获取当月最后一天
     *
     * @return
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return dateToStr19(calendar.getTime());
    }

    /**
     * 获取当周第一天
     *
     * @param day
     * @return
     */
    public static String getFirstDayOfWeek(String day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(day));
        } catch (ParseException e) {
            log.error("日期转换出现异常");
        }

        //当前日期是周几
        int weekday = cal.get(Calendar.DAY_OF_WEEK);
        if (weekday == 1) {
            //如果是周日,减一
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        //周一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        int dy = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - dy);

        return sdf.format(cal.getTime());
    }

    /**
     * 获取当周最后一天
     *
     * @param day
     * @return
     */
    public static String getLastDayOfWeek(String day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(day));
        } catch (ParseException e) {
            log.error("日期转换出现异常");
        }

        int weekday = cal.get(Calendar.DAY_OF_WEEK);//当前日期是周几
        if (weekday != 1) {
            cal.add(Calendar.DATE, 8 - weekday);
            return sdf.format(cal.getTime());
        }

        return sdf.format(cal.getTime());
    }

    public static List<String> getMonthFrom202101(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String time = "2021-01-01";
        try {
            Date startDate = sdf.parse(time);
            return getMonthBetweenDate(startDate, date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

    }


    public static List<Date> getDayFrom202208(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String time = "2022-08-01";
        try {
            Date startDate = sdf.parse(time);
            return getDayBetweenDate(startDate, date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取两个日期之间的所有月份 (年月)
     *
     * @param startDate
     * @param endDate
     * @return：YYYY-MM
     */
    public static List<String> getMonthBetweenDate(Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        // 声明保存日期集合
        List<String> list = new ArrayList<String>();
        //用Calendar 进行日期比较判断
        Calendar calendar = Calendar.getInstance();
        while (startDate.getTime() <= endDate.getTime()) {
            // 把日期添加到集合
            list.add(sdf.format(startDate));
            // 设置日期
            calendar.setTime(startDate);
            //把日期增加一天
            calendar.add(Calendar.MONTH, 1);
            // 获取增加后的日期
            startDate = calendar.getTime();
        }

        return list;
    }

    /**
     * 获取两个日期之间的所有月份 (年月日)
     *
     * @param startDate
     * @param endDate
     * @return：YYYY-MM
     */
    public static List<Date> getDayBetweenDate(Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 声明保存日期集合
        List<Date> list = new ArrayList<Date>();
        //用Calendar 进行日期比较判断
        Calendar calendar = Calendar.getInstance();
        while (startDate.getTime() <= endDate.getTime()) {
            // 把日期添加到集合
            list.add(startDate);
            // 设置日期
            calendar.setTime(startDate);
            //把日期增加一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            // 获取增加后的日期
            startDate = calendar.getTime();
        }

        return list;
    }

    /**
     * 获取某年某月的第一天
     */
    public static String getFirstDayOfMonth(String time) {
        String[] strings = time.split("-");
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.parseInt(strings[0]));
        //设置月份
        cal.set(Calendar.MONTH, Integer.parseInt(strings[1]) - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * 获取某月的最后一天
     */
    public static String getLastDayOfMonth(String time) {
        String[] strings = time.split("-");
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.parseInt(strings[0]));
        //设置月份
        cal.set(Calendar.MONTH, Integer.parseInt(strings[1]) - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }


    /**
     * 获取近七天的日期，以单个字符串形式返回，日期之间用逗号分隔。
     * @return 近七天日期的字符串
     */
    public static String getRecentSevenDaysAsString() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd");
        LocalDate currentDate = LocalDate.now();
        List<String> dates = new ArrayList<>();

        for (int i = 0; i <= 6; i++) {
            dates.add(formatter.format(currentDate.minusDays(i)));
        }

        // 将日期列表转换为一个用逗号分隔的字符串
        return dates.stream()
            .collect(Collectors.joining(", "));
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 两个日期之间相差的天数
     */
    public static int daysBetween(Date startDate, Date endDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);
        long time1 = cal.getTimeInMillis();

        cal.setTime(endDate);
        long time2 = cal.getTimeInMillis();

        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    public static void main(String[] args) {

        System.out.println(getRecentSevenDaysAsString());

    }
}
