package com.ruoyi.quartz.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目打分记录表(ProjectScoreRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-05-27 17:28:56
 */
public class ProjectScoreRecord implements Serializable {
    private static final long serialVersionUID = 420788159301610744L;

    private Integer id;
/**
     * 项目id
     */
    private Integer projectId;
/**
     * 分数
     */
    private Double score;
/**
     * 打分人
     */
    private String scorer;
/**
     * 创建时间
     */
    private Date createTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getScorer() {
        return scorer;
    }

    public void setScorer(String scorer) {
        this.scorer = scorer;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}

