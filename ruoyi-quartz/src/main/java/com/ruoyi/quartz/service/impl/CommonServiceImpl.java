package com.ruoyi.quartz.service.impl;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
//import com.cvit.common.core.domain.R;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.quartz.enums.UploadType;
import com.ruoyi.quartz.service.CommonService;
import com.ruoyi.quartz.util.AliyunOssUploadUtils;
import com.ruoyi.quartz.util.DateUtil;
import com.ruoyi.quartz.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.MimetypesFileTypeMap;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022-10-20 10:28
 **/
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {
    @Autowired
    private AliyunOssUploadUtils aliyunOssUploadUtils;
    @Override
    public List<String> uploadFilesCompress(MultipartFile[] files, String type) {
        List<String> fileUrls = new ArrayList<>();
        for (MultipartFile file : files) {
            String originalFilename = file.getOriginalFilename();
            if (StrUtil.isBlank(originalFilename)) {
                originalFilename = IdUtils.simpleUUID();
            }
            String contentType = file.getContentType();
            if (StrUtil.isBlank(contentType)) {
                contentType = getContentType(type);
            }
            //对图片进行压缩;非图片暂不压缩（需要不同的压缩方案）
            if (StringUtils.isNotEmpty(FileUtil.getImgSuffix(contentType))) {
                String imagePath = "";
                String os = System.getProperty("os.name");
                if (os.toLowerCase().startsWith("win")) {
                    imagePath = "D:" + File.separator + "tmp" + File.separator + DateUtil.getNowDate1String();
                } else {
                    imagePath = "/tmp" + File.separator + DateUtil.getNowDate1String();
                }

                String originalNameNotSuffix = originalFilename.substring(0, originalFilename.indexOf("."));
                String suffix = originalFilename.substring(originalFilename.indexOf("."), originalFilename.length());
                //统一阿里云oss固定后缀名格式 【随机生成的文件名__文件原始名称.后缀名】
                String fileName = IdUtil.getSnowflake().nextIdStr() + "__" + originalNameNotSuffix + suffix;
                log.info("\n -- fileContentType:{} , fileOriginName:{}, suffix:{} ,finalFileName:{}--", contentType,
                        originalFilename,
                        suffix, fileName);
                FileUtil.createDirIfNotExists(imagePath);
                // 如： /opt/image/userFace/xxxx.jpg
                String fullFilePath = imagePath + File.separator + fileName;
                log.info("fullFilePath:{}, fileSeparator:{}", fullFilePath, File.separator);
                File compressedFile = new File(fullFilePath);
                try {
                    file.transferTo(compressedFile);
                } catch (IOException e) {
                    log.error("图片上传发生IO异常" + e.getMessage());
                }
                // 压缩图片
                FileUtil.compressPicture(fullFilePath);
                try (InputStream in = new FileInputStream(fullFilePath)) {
                    String fileUrl =  aliyunOssUploadUtils.uploadToAliyun(fileName, in, compressedFile.length(), suffix);
                    fileUrls.add(fileUrl);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("uploadFiles exception:", e);
                }
            } else {
                try (InputStream in = file.getInputStream()) {
                    String fileUrl =  aliyunOssUploadUtils.uploadToAliyun(originalFilename, in, file.getSize(),
                            contentType);
                    fileUrls.add(fileUrl);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("uploadFiles exception:", e);
                }
            }
        }
        return fileUrls;
    }

    @Override
    public List<String> uploadFilesCompress(File[] files, UploadType type) {
        List<String> fileUrls = new ArrayList<>();
        if (UploadType.IMAGE != type) {
            for (File file : files) {
                String originalFilename = file.getName();
                if (StrUtil.isBlank(originalFilename)) {
                    originalFilename = IdUtils.simpleUUID();
                }
                String contentType = null;
                try {
                    contentType = new MimetypesFileTypeMap().getContentType(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try (InputStream in = new FileInputStream(file)) {
                    String fileUrl =  aliyunOssUploadUtils.uploadToAliyun(originalFilename, in, file.length(),
                            contentType);
                    fileUrls.add(fileUrl);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("uploadFiles exception:", e);
                }
            }
            return fileUrls;
            //只对图片类型的进行压缩,这里不在重复校验文件的Content-type
        } else {
            for (File file : files) {
                String originalFilename = file.getName();
                if (StrUtil.isBlank(originalFilename)) {
                    originalFilename = IdUtils.simpleUUID();
                }
                String contentType = null;
                try {
                    contentType = new MimetypesFileTypeMap().getContentType(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //对图片进行压缩;非图片暂不压缩（需要不同的压缩方案）
                if (StringUtils.isNotEmpty(FileUtil.getImgSuffix(contentType))) {
                    String imagePath = "";
                    String os = System.getProperty("os.name");
                    if (os.toLowerCase().startsWith("win")) {
                        imagePath = "D:" + File.separator + "tmp" + File.separator + DateUtil.getNowDate1String();
                    } else {
                        imagePath = "/tmp" + File.separator + DateUtil.getNowDate1String();
                    }

                    String originalNameNotSuffix = originalFilename.substring(0, originalFilename.indexOf("."));
                    String suffix = originalFilename.substring(originalFilename.indexOf("."),
                            originalFilename.length());
                    //统一阿里云oss固定后缀名格式 【随机生成的文件名__文件原始名称.后缀名】
                    String fileName = IdUtil.getSnowflake().nextIdStr() + "__" + originalNameNotSuffix + suffix;
                    log.info("\n -- fileContentType:{} , fileOriginName:{}, suffix:{} ,finalFileName:{}--", contentType,
                            originalFilename,
                            suffix, fileName);
                    FileUtil.createDirIfNotExists(imagePath);
                    // 如： /opt/image/userFace/xxxx.jpg
                    String fullFilePath = imagePath + File.separator + fileName;
                    log.info("fullFilePath:{}, fileSeparator:{}", fullFilePath, File.separator);
                    File compressedFile = new File(fullFilePath);
                    try {
//                        file.transferTo(compressedFile);
                        cn.hutool.core.io.FileUtil.copy(file, compressedFile, true);
                    } catch (IORuntimeException e) {
                        log.error("图片上传发生IO异常" + e.getMessage());
                    }
                    // 压缩图片
                    FileUtil.compressPicture(fullFilePath);
                    try (InputStream in = new FileInputStream(fullFilePath)) {
                        String fileUrl =  aliyunOssUploadUtils.uploadToAliyun(fileName, in, compressedFile.length(),
                                suffix);
                        fileUrls.add(fileUrl);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("uploadFiles exception:", e);
                    }
                }
            }
            return fileUrls;
        }
    }

    @Override
    public R upload(MultipartFile[] files) {
        List<String> fileUrls = new ArrayList<>();
        for (MultipartFile file : files) {
            String contentType = file.getContentType();
            String originalFilename = file.getOriginalFilename();
            //String originalNameNotSuffix = originalFilename.substring(0, originalFilename.indexOf("."));
            String originalNameNotSuffix = originalFilename.substring(0, originalFilename.lastIndexOf("."));
            //String suffix = originalFilename.substring(originalFilename.indexOf("."), originalFilename.length());
            String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            //统一阿里云oss固定后缀名格式 【随机生成的文件名__文件原始名称.后缀名】
            String fileName = IdUtil.getSnowflake().nextIdStr() + "__" + originalNameNotSuffix + suffix;
            // 限制文件类型
            Set<String> allowedTypes = new HashSet<>(Arrays.asList(".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".jpg", ".jpeg", ".png", ".gif"));
            if (!allowedTypes.contains(suffix)) {

                return R.fail("不支持的文件类型!");
            }
            // 限制文件大小
            long fileSize = file.getSize();
            //if (fileSize > 20 * 1024 * 1024) { // 20M
            //    return R.fail("单个文件大小不能超过20M!");
            //}
            boolean isImage = suffix.equalsIgnoreCase(".jpg") || suffix.equalsIgnoreCase(".jpeg") || suffix.equalsIgnoreCase(".png") || suffix.equalsIgnoreCase(".gif");
            long maxSize = isImage ? 10 * 1024 * 1024 : 20 * 1024 * 1024; // 图片10M，文件20M
            if (fileSize > maxSize) {
                return R.fail("单个文件大小不能超过" + (isImage ? "10M" : "20M") + "!");
            }
            try {
                String fileUrl =  aliyunOssUploadUtils.uploadToAliyun(fileName, file.getInputStream(), file.getSize(),
                    contentType);
                fileUrls.add(fileUrl);
            } catch (MaxUploadSizeExceededException uploadSizeExce) {
                return R.fail("文件过大,单个文件100M，多个文件1000!");
            } catch (IOException ossUploadException) {
                log.error("\n -- oss upload filed, reason:{}", ossUploadException.getMessage());
            }
        }
        return R.ok(fileUrls);
    }

    private String getContentType(String type) {
        if (type.equals(UploadType.IMAGE)) {
            return "image/jpeg";
        } else if (type.equals(UploadType.AUDIO)) {
            return "audio/mp3/mp4";
        } else {
            return "text/plain";
        }
    }

    private void checkImageFile(MultipartFile[] files) {
        for (MultipartFile file : files) {
            log.info("Content-type:{}", file.getContentType());
            if (StringUtils.isEmpty(FileUtil.getImgSuffix(file.getContentType()))) {
                throw new IllegalArgumentException("非图片");
            }
            double fileSize = file.getSize() / 1024d / 1024d;
            if (fileSize > 15) {
                throw new IllegalArgumentException("单个图片不能大于15M");
            }
            if (file.isEmpty()) {
                throw new IllegalArgumentException("图片文件不存在");
            }
        }
    }

    private void checkAudioFile(MultipartFile[] files) {
        for (MultipartFile file : files) {
            double fileSize = file.getSize() / 1024d / 1024d;
            if (fileSize > 15) {
                throw new IllegalArgumentException("单个音频文件不能大于15M");
            }
            if (file.isEmpty()) {
                throw new IllegalArgumentException("音频文件不存在");
            }
        }
    }
}
