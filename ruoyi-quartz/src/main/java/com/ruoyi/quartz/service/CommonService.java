package com.ruoyi.quartz.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.quartz.enums.UploadType;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface CommonService {

    List<String> uploadFilesCompress(MultipartFile[] files, String type);

    /**
     * 此方法解决异步任务调用MultipartFile的时候，主线程会清空临时目录导致文件找不到
     * 故而参数由MultipartFile[] 转File[] 实现压缩.
     * @param files
     * @param type
     * @return
     */
    List<String> uploadFilesCompress(File[] files, UploadType type);

    /**
     * 可上传所有类型
     * @param files
     * @return
     */
    R upload(MultipartFile[] files);
}
