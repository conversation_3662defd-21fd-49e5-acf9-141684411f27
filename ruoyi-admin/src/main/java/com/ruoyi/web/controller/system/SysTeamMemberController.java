package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysTeamMember;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysTeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队成员信息Controller
 */
@RestController
@RequestMapping("/system/teamMember")
public class SysTeamMemberController extends BaseController {
    @Autowired
    private ISysTeamMemberService sysTeamMemberService;

    /**
     * 查询团队成员信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:teamMember:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysTeamMember sysTeamMember) {
        startPage();
        List<SysTeamMember> list = sysTeamMemberService.selectSysTeamMemberList(sysTeamMember);
        return getDataTable(list);
    }

    /**
     * 根据用户ID获取团队成员列表
     */
    @GetMapping("/user/{userId}")
    public AjaxResult getTeamMembersByUserId(@PathVariable("userId") Long userId) {
        return success(sysTeamMemberService.selectTeamMembersByUserId(userId));
    }

    /**
     * 获取团队成员详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:teamMember:query')")
    // @GetMapping(value = "/{memberId}")
    // public AjaxResult getInfo(@PathVariable("memberId") Long memberId) {
    //     // return success(sysTeamMemberService.selectSysTeamMemberById(memberId));
    // }

    /**
     * 新增团队成员信息
     */
    @PreAuthorize("@ss.hasPermi('system:teamMember:add')")
    @PostMapping
    public AjaxResult add(@RequestBody SysTeamMember sysTeamMember) {
        return toAjax(sysTeamMemberService.insertSysTeamMember(sysTeamMember));
    }

    /**
     * 批量新增团队成员信息
     */
    @PreAuthorize("@ss.hasPermi('system:teamMember:add')")
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<SysTeamMember> teamMembers) {
        return toAjax(sysTeamMemberService.batchInsertTeamMember(teamMembers));
    }

    /**
     * 修改团队成员信息
     */
    @PreAuthorize("@ss.hasPermi('system:teamMember:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody SysTeamMember sysTeamMember) {
        return toAjax(sysTeamMemberService.updateSysTeamMember(sysTeamMember));
    }

    /**
     * 删除团队成员信息
     */
    @PreAuthorize("@ss.hasPermi('system:teamMember:remove')")
    @DeleteMapping("/{memberIds}")
    public AjaxResult remove(@PathVariable List<Long> memberIds) {
        return toAjax(sysTeamMemberService.deleteSysTeamMemberByIds(memberIds));
    }

    /**
     * 根据用户ID删除团队成员信息
     */
    @PreAuthorize("@ss.hasPermi('system:teamMember:remove')")
    @DeleteMapping("/user/{userId}")
    public AjaxResult removeByUserId(@PathVariable Long userId) {
        return toAjax(sysTeamMemberService.deleteSysTeamMemberByUserId(userId));
    }
}
