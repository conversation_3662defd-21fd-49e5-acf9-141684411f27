package com.ruoyi.web.controller.system;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 行政区划
 *
 * <AUTHOR>
@RestController
@RequestMapping("/system/area")
public class SysAreaController {

    /**
     * 查询行政区划树
     */
    @GetMapping("/getAreaTree")
    public AjaxResult getAreaTree() throws IOException {
        InputStream inputStream = null;
        BufferedReader reader = null;

        try {
            inputStream = SysAreaController.class.getClassLoader().getResourceAsStream("1.json");

            if (inputStream == null) {
                System.err.println("File not found in resources directory.");
                return AjaxResult.error("File not found");
            }

            reader = new BufferedReader(new InputStreamReader(inputStream));
            String content = reader.lines().collect(Collectors.joining());

            //ObjectMapper mapper = new ObjectMapper();
            //Object json = mapper.readValue(content, Object.class);
            //
            //String formattedJson = mapper.writeValueAsString(json);
            ObjectMapper mapper = new ObjectMapper();
            List<Tree<String>> areaList = mapper.readValue(content, new TypeReference<List<Tree<String>>>() {});
            return AjaxResult.success(areaList);

        } catch (IOException e) {
            System.err.println("Error reading JSON file: " + e.getMessage());
            return AjaxResult.error("Failed to process JSON data");
        } finally {
            // 确保资源被关闭
            if (reader != null) try {
                reader.close();
            } catch (IOException e) {
            }
            if (inputStream != null) try {
                inputStream.close();
            } catch (IOException e) {
            }
        }
    }

}
