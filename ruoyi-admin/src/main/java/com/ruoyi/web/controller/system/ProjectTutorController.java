package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.ProjectTutor;
import com.ruoyi.system.service.IProjectTutorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目辅导老师信息Controller
 *
 * <AUTHOR>
 * @since 2025-06-01
 */
@RestController
@RequestMapping("/system/projectTutor")
public class ProjectTutorController extends BaseController {
    @Autowired
    private IProjectTutorService projectTutorService;

    /**
     * 查询项目辅导老师列表
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectTutor projectTutor) {
        startPage();
        List<ProjectTutor> list = projectTutorService.selectList(projectTutor);
        return getDataTable(list);
    }

    /**
     * 获取项目辅导老师详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(projectTutorService.selectById(id));
    }

    /**
     * 根据项目ID获取辅导老师列表
     */
    @GetMapping(value = "/project/{projectId}")
    public AjaxResult getListByProjectId(@PathVariable("projectId") Integer projectId) {
        return success(projectTutorService.selectByProjectId(projectId));
    }

    /**
     * 新增项目辅导老师
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:add')")
    @PostMapping
    public AjaxResult add(@RequestBody ProjectTutor projectTutor) {
        return toAjax(projectTutorService.insert(projectTutor) != null ? 1 : 0);
    }

    /**
     * 批量新增项目辅导老师
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:add')")
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<ProjectTutor> projectTutors) {
        return toAjax(projectTutorService.batchInsert(projectTutors));
    }

    /**
     * 修改项目辅导老师
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectTutor projectTutor) {
        return toAjax(projectTutorService.update(projectTutor) != null ? 1 : 0);
    }

    /**
     * 删除项目辅导老师
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:remove')")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        int successCount = 0;
        for (Integer id : ids) {
            if (projectTutorService.deleteById(id)) {
                successCount++;
            }
        }
        return toAjax(successCount);
    }

    /**
     * 根据项目ID删除辅导老师
     */
    @PreAuthorize("@ss.hasPermi('system:projectTutor:remove')")
    @DeleteMapping("/project/{projectId}")
    public AjaxResult removeByProjectId(@PathVariable("projectId") Integer projectId) {
        return toAjax(projectTutorService.deleteByProjectId(projectId) ? 1 : 0);
    }
} 