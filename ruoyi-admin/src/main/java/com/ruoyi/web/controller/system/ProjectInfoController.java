package com.ruoyi.web.controller.system;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysParentInfo;
import com.ruoyi.common.core.domain.entity.SysTeamMember;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.ProjectInfo;
import com.ruoyi.system.domain.ProjectTutor;
import com.ruoyi.system.domain.ProjectScore;
import com.ruoyi.system.service.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * 作品信息表(ProjectInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-27 14:26:41
 */
@RestController
@RequestMapping("/projectInfo")
public class ProjectInfoController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ProjectInfoController.class);

    /**
     * 学生标识
     */
    public static final String STUDENT = "student";
    /**
     * 老师标识
     */
    public static final String TEACHER = "teacher";

    @Value("${file.student}")
    private String studentFile;

    @Value("${file.teacher}")
    private String teacherFile;

    @Value("${file.declaration}")
    private String declarationFile;

    @Value("${judge.comment1}")
    private String judgeComment1;

    @Value("${judge.comment2}")
    private String judgeComment2;

    @Value("${judge.comment3}")
    private String judgeComment3;

    /**
     * 服务对象
     */
    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysTeamMemberService teamMemberService;

    @Autowired
    private IProjectTutorService projectTutorService;
    @Autowired
    private ISysParentInfoService parentInfoService;
    @Autowired
    private ProjectScoreService projectScoreService;

    /**
     * 分页查询
     *
     * @param pageNum
     * @param pageSize
     * @return 查询结果
     */
    @GetMapping("/queryByPage")
    public AjaxResult queryByPage(@RequestParam(name = "pageNum") Integer pageNum,
                                  @RequestParam(name = "pageSize") Integer pageSize,
                                  @RequestParam(name = "category", required = false) Integer category,
                                  @RequestParam(name = "name", required = false) String name,
                                  @RequestParam(name = "userName", required = true) String userName,
                                  @RequestParam(name = "district", required = false) String district,
                                  @RequestParam(name = "participantType", required = false) String participantType,
                                  @RequestParam(name = "articleStatus", required = false) Integer articleStatus,
                                  @RequestParam(name = "sortField", required = false) String sortField,
                                  @RequestParam(name = "sortOrder", required = false) String sortOrder) {
        return AjaxResult.success(this.projectInfoService.queryByPage(pageNum, pageSize, category, name, userName, district,
                participantType, articleStatus, sortField, sortOrder));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/queryById")
    public AjaxResult queryById(@RequestParam(name = "id") Integer id) {
        return AjaxResult.success(this.projectInfoService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param projectInfo 实体
     * @return 新增结果
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ProjectInfo projectInfo) {
        projectInfo.setUserName(getUsername());
        return this.projectInfoService.insert(projectInfo);
    }

    /**
     * 编辑数据
     *
     * @param projectInfo 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody ProjectInfo projectInfo) {
        if (ObjectUtils.isEmpty(projectInfo) || ObjectUtils.isEmpty(projectInfo.getId())) {
            return AjaxResult.error("参数有误");
        }

        Integer id = projectInfo.getId();
        ProjectInfo project = projectInfoService.queryById(id);
        boolean banEdit = Objects.equals(project.getDistrictAuditStatus(), 1) && !Objects.equals(project.getAuditStatus(), 2);
        if (Objects.equals(project.getAuditStatus(), 1) || banEdit) {
            return AjaxResult.error("当前初审或终审通过，禁止修改");
        }
        if (Objects.equals(project.getArticleStatus(), 3)) {
            if (StringUtils.isBlank(project.getDeclarationUrl())) {
                project.setArticleStatus(0);
            } else {
                project.setArticleStatus(1);
            }
        }

        if (projectInfo.getScore() != null) {
            projectInfo.setScoreTime(new Date());
            projectInfo.setScorer(getUsername());
        }

        return AjaxResult.success(this.projectInfoService.update(projectInfo));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/delete/{id}")
    public AjaxResult deleteById(@PathVariable Integer id) {
        return AjaxResult.success(this.projectInfoService.deleteById(id));
    }

    /**
     * 根据项目ID获取辅导老师列表
     *
     * @param projectId 项目ID
     * @return 辅导老师列表
     */
    @GetMapping("/tutors/{projectId}")
    public AjaxResult getTutorsByProjectId(@PathVariable("projectId") Integer projectId) {
        List<ProjectTutor> tutors = projectTutorService.selectByProjectId(projectId);
        return AjaxResult.success(tutors);
    }

    /**
     * 新增项目辅导老师
     *
     * @param projectTutor 辅导老师信息
     * @return 新增结果
     */
    @PostMapping("/tutors")
    public AjaxResult addTutor(@RequestBody ProjectTutor projectTutor) {
        // 验证项目是否存在
        ProjectInfo projectInfo = projectInfoService.queryById(projectTutor.getProjectId());
        if (projectInfo == null) {
            return AjaxResult.error("项目不存在");
        }

        // 验证辅导老师数量
        List<ProjectTutor> existingTutors = projectTutorService.selectByProjectId(projectTutor.getProjectId());
        if (existingTutors.size() >= 2) {
            return AjaxResult.error("一个项目最多只能有两位辅导老师");
        }

        // 设置排序序号
        projectTutor.setSortOrder(existingTutors.size() + 1);

        ProjectTutor result = projectTutorService.insert(projectTutor);

        // 如果是第一位辅导老师，更新项目的tutorName和tutorContact字段
        if (projectTutor.getSortOrder() == 1) {
            projectInfo.setTutorName(projectTutor.getName());
            projectInfo.setTutorContact(projectTutor.getContact());
            projectInfoService.update(projectInfo);
        }

        return AjaxResult.success(result);
    }

    /**
     * 批量新增项目辅导老师
     *
     * @param projectTutors 辅导老师列表
     * @return 新增结果
     */
    @PostMapping("/tutors/batch")
    public AjaxResult batchAddTutors(@RequestBody List<ProjectTutor> projectTutors) {
        if (CollectionUtils.isEmpty(projectTutors)) {
            return AjaxResult.error("辅导老师信息不能为空");
        }

        // 验证项目是否存在
        Integer projectId = projectTutors.get(0).getProjectId();
        ProjectInfo projectInfo = projectInfoService.queryById(projectId);
        if (projectInfo == null) {
            return AjaxResult.error("项目不存在");
        }

        // 验证辅导老师数量
        if (projectTutors.size() > 2) {
            return AjaxResult.error("一个项目最多只能有两位辅导老师");
        }

        // 先删除原有的辅导老师信息
        projectTutorService.deleteByProjectId(projectId);

        // 设置排序序号
        for (int i = 0; i < projectTutors.size(); i++) {
            projectTutors.get(i).setSortOrder(i + 1);
        }

        int result = projectTutorService.batchInsert(projectTutors);

        // 更新项目的tutorName和tutorContact字段
        if (projectTutors.size() > 0) {
            ProjectTutor firstTutor = projectTutors.get(0);
            projectInfo.setTutorName(firstTutor.getName());
            projectInfo.setTutorContact(firstTutor.getContact());
            projectInfoService.update(projectInfo);
        }

        return AjaxResult.success(result);
    }

    /**
     * 修改项目辅导老师
     *
     * @param projectTutor 辅导老师信息
     * @return 修改结果
     */
    @PutMapping("/tutors")
    public AjaxResult updateTutor(@RequestBody ProjectTutor projectTutor) {
        ProjectTutor result = projectTutorService.update(projectTutor);

        // 如果是第一位辅导老师，更新项目的tutorName和tutorContact字段
        if (projectTutor.getSortOrder() == 1) {
            ProjectInfo projectInfo = projectInfoService.queryById(projectTutor.getProjectId());
            if (projectInfo != null) {
                projectInfo.setTutorName(projectTutor.getName());
                projectInfo.setTutorContact(projectTutor.getContact());
                projectInfoService.update(projectInfo);
            }
        }

        return AjaxResult.success(result);
    }

    /**
     * 删除项目辅导老师
     *
     * @param id 辅导老师ID
     * @return 删除结果
     */
    @DeleteMapping("/tutors/{id}")
    public AjaxResult deleteTutor(@PathVariable("id") Integer id) {
        // 获取辅导老师信息
        ProjectTutor tutor = projectTutorService.selectById(id);
        if (tutor == null) {
            return AjaxResult.error("辅导老师不存在");
        }

        // 删除辅导老师
        boolean result = projectTutorService.deleteById(id);

        // 如果是第一位辅导老师，需要更新项目的tutorName和tutorContact字段
        if (result && tutor.getSortOrder() == 1) {
            // 查询是否还有其他辅导老师
            List<ProjectTutor> remainingTutors = projectTutorService.selectByProjectId(tutor.getProjectId());

            ProjectInfo projectInfo = projectInfoService.queryById(tutor.getProjectId());
            if (projectInfo != null) {
                if (CollectionUtils.isNotEmpty(remainingTutors)) {
                    // 将第二位辅导老师设为第一位
                    ProjectTutor newFirstTutor = remainingTutors.get(0);
                    newFirstTutor.setSortOrder(1);
                    projectTutorService.update(newFirstTutor);

                    projectInfo.setTutorName(newFirstTutor.getName());
                    projectInfo.setTutorContact(newFirstTutor.getContact());
                } else {
                    // 没有辅导老师了，清空字段
                    projectInfo.setTutorName(null);
                    projectInfo.setTutorContact(null);
                }
                projectInfoService.update(projectInfo);
            }
        }

        return AjaxResult.success(result);
    }

    /**
     * 创作者声明下载模板
     *
     * @return
     */
    @GetMapping("/downResult")
    public AjaxResult downResult(@RequestParam(name = "idFlag") String idFlag) {
        String url = "";
        if (STUDENT.equals(idFlag)) {
            url = studentFile;
        } else if (TEACHER.equals(idFlag)) {
            url = teacherFile;
        } else {
            url = "http://39.107.80.97/files/创作者声明.docx";
        }
        return AjaxResult.success("下载成功！", url);
    }

    /**
     * 申报表简单模板下载
     */
    @GetMapping("/downDeclaration")
    public AjaxResult downDeclaration() {
        String url = declarationFile;
        return AjaxResult.success("下载成功！", url);
    }

    /**
     * 申报
     *
     * @param req 实体
     * @return 编辑结果
     */
    @PostMapping("/declaration")
    public AjaxResult declaration(@RequestBody Map<String, Object> req) {
        Integer id = (Integer) req.get("id");
        String declarationUrl = (String) req.get("declarationUrl");
        if (id == null || StringUtils.isBlank(declarationUrl)) {
            return AjaxResult.error("参数有误");
        }

        ProjectInfo projectInfo = projectInfoService.queryById(id);
        if (projectInfo == null) {
            return AjaxResult.error("该作品不存在");
        }
        Integer districtAuditStatus = projectInfo.getDistrictAuditStatus();
        Integer auditStatus = projectInfo.getAuditStatus();
        boolean banEdit = Objects.equals(districtAuditStatus, 1) && !Objects.equals(auditStatus, 2);
        if (Objects.equals(auditStatus, 1) || banEdit) {
            return AjaxResult.error("审核通过禁止申报");
        }

        projectInfo.setDeclarationUrl(declarationUrl);
        projectInfo.setArticleStatus(1);
        projectInfo.setUpdatedTime(new Date());
        projectInfoService.updateDeclaration(projectInfo);

        return AjaxResult.success("申报成功！");
    }

    /**
     * 审核
     *
     * @param req 实体
     * @return 编辑结果
     */
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody Map<String, Object> req) {
        Integer id = (Integer) req.get("id");
        Integer auditStatus = (Integer) req.get("auditStatus");
        String auditRemark = (String) req.get("auditRemark");
        String auditor = (String) req.get("auditor");
        if (id == null || ObjectUtils.isEmpty(auditStatus)) {
            return AjaxResult.error("参数有误");
        }

        ProjectInfo projectInfo = projectInfoService.queryById(id);
        if (projectInfo == null) {
            return AjaxResult.error("该作品不存在");
        }
        if (!Objects.equals(projectInfo.getDistrictAuditStatus(), 1)) {
            return AjaxResult.error("该作品暂未初审");
        }
        // 终审通过，则作品状态改为终审通过
        if (Objects.equals(auditStatus, 1)) {
            projectInfo.setArticleStatus(4);
        } else {
            projectInfo.setArticleStatus(5);
        }
        projectInfo.setAuditStatus(auditStatus);
        projectInfo.setAuditRemark(auditRemark);

        SysUser user = userService.selectUserByUserName(auditor);
        if (user != null) {
            projectInfo.setAuditor(StringUtils.isNotBlank(user.getNickName()) ? user.getNickName() : user.getUserName());
        }
        projectInfoService.updateAudit(projectInfo);

        return AjaxResult.success("审核成功！");
    }

    /**
     * 作品申报表模板下载
     */
    @GetMapping("/downByProjectId")
    public ResponseEntity<ByteArrayResource> downByProjectId(@RequestParam(name = "projectId") Integer projectId) {
        if (projectId == null) {
            return ResponseEntity.badRequest().build();
        }

        ProjectInfo projectInfo = projectInfoService.queryById(projectId);
        if (projectInfo == null) {
            return ResponseEntity.notFound().build();
        }
        String userName = projectInfo.getUserName();
        SysUser user = userService.selectUserByUserName(userName);
        if (user == null) {
            return ResponseEntity.notFound().build();
        }
        List<SysTeamMember> teamMembers = teamMemberService.selectTeamMembersByUserId(user.getUserId());
        user.setTeamMembers(teamMembers);

        // 查询作品作者父母信息
        List<SysParentInfo> sysParentInfos = parentInfoService.selectParentInfosByUserId(user.getUserId());
        user.setParents(sysParentInfos);

        return projectInfoService.downByProjectId(projectInfo, user);
    }

    /**
     * 推荐
     *
     * @param req 实体
     * @return 编辑结果
     */
    @PostMapping("/recommend")
    public AjaxResult recommend(@RequestBody Map<String, Object> req) {
        List<Integer> ids = JSONObject.parseObject(req.get("ids").toString(), List.class);
        Integer districtAuditStatus = (Integer) req.get("recommendStatus");
        String districtAuditRemark = (String) req.get("recommendRemark");
        String districtAuditor = (String) req.get("districtAuditor");
        if (CollectionUtils.isEmpty(ids) || ObjectUtils.isEmpty(districtAuditStatus)) {
            return AjaxResult.error("参数有误");
        }

        ProjectInfo projectInfo = new ProjectInfo();
        for (Integer id : ids) {
            projectInfo = projectInfoService.queryById(id);
            if (projectInfo == null) {
                return AjaxResult.error("作品不存在");
            }
            // 判断作品是否待审核状态
            if (!Objects.equals(projectInfo.getArticleStatus(), 1)) {
                return AjaxResult.error("作品非待审核状态");
            }
            if (Objects.equals(projectInfo.getDistrictAuditStatus(), 1)) {
                return AjaxResult.error("作品已初审通过，请勿重新提交");
            }

            // 推荐成功后，将作品状态改为初审通过
            if (Objects.equals(districtAuditStatus, 1)) {
                projectInfo.setArticleStatus(2);
            } else {
                projectInfo.setArticleStatus(3);
            }
            projectInfo.setDistrictAuditStatus(districtAuditStatus);
            projectInfo.setDistrictAuditRemark(districtAuditRemark);

            SysUser user = userService.selectUserByUserName(districtAuditor);
            if (user != null) {
                projectInfo.setDistrictAuditor(StringUtils.isNotBlank(user.getNickName()) ? user.getNickName() : user.getUserName());
            }
            projectInfoService.updateAudit(projectInfo);
        }

        return AjaxResult.success("推荐成功");
    }

    /**
     * 批量导出作品信息
     *
     * @param ids 要导出的作品ID列表
     * @return Excel文件
     */
    @PostMapping("/export")
    public ResponseEntity<ByteArrayResource> export(@RequestBody List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ResponseEntity.badRequest().build();
        }

        // 查询所有作品信息
        List<ProjectInfo> projectInfoList = new ArrayList<>();
        for (Integer id : ids) {
            ProjectInfo projectInfo = projectInfoService.queryById(id);
            if (projectInfo != null) {
                projectInfoList.add(projectInfo);
            }
        }

        if (CollectionUtils.isEmpty(projectInfoList)) {
            return ResponseEntity.notFound().build();
        }

        return projectInfoService.export(projectInfoList);
    }

    /**
     * 项目评分
     *
     * @param req 评分请求参数
     * @return 评分结果
     */
    @PostMapping("/score")
    public AjaxResult scoreProject(@RequestBody Map<String, Object> req) {
        // 参数校验
        Integer projectId = (Integer) req.get("projectId");
        Object scoreObj = req.get("score");
        String comment = (String) req.get("comment");

        if (projectId == null) {
            return AjaxResult.error("项目ID不能为空");
        }
        if (scoreObj == null) {
            return AjaxResult.error("分数不能为空");
        }

        BigDecimal score;
        try {
            if (scoreObj instanceof Integer) {
                score = new BigDecimal((Integer) scoreObj);
            } else if (scoreObj instanceof Double) {
                score = BigDecimal.valueOf((Double) scoreObj);
            } else if (scoreObj instanceof String) {
                score = new BigDecimal((String) scoreObj);
            } else {
                return AjaxResult.error("分数格式不正确");
            }
        } catch (NumberFormatException e) {
            return AjaxResult.error("分数格式不正确");
        }

        return projectScoreService.scoreProject(projectId, score, comment);
    }

    /**
     * 获取项目评分信息
     *
     * @param projectId 项目ID
     * @return 评分信息
     */
    @GetMapping("/score/{projectId}")
    public AjaxResult getProjectScore(@PathVariable("projectId") Integer projectId) {
        if (projectId == null) {
            return AjaxResult.error("项目ID不能为空");
        }

        ProjectScore projectScore = projectScoreService.selectByProjectIdWithJudgeNames(projectId);
        return AjaxResult.success(projectScore);
    }

    /**
     * 获取默认评语
     *
     * @return 评分信息
     */
    @GetMapping("/score/getDefaultComment")
    public AjaxResult getDefaultComment() {
        if (StringUtils.isBlank(judgeComment1)) {
            return AjaxResult.error("评语未配置");
        }

        Map<String, String> judgeComment = new HashMap<>();
        judgeComment.put("defaultComment1", judgeComment1);
        judgeComment.put("defaultComment2", judgeComment2);
        judgeComment.put("defaultComment3", judgeComment3);
        return AjaxResult.success(judgeComment);
    }

    /**
     * 检查当前用户是否已经对指定项目进行过评分
     *
     * @param projectId 项目ID
     * @return 评分状态信息
     */
    @GetMapping("/score/checkScored/{projectId}")
    public AjaxResult checkJudgeScored(@PathVariable("projectId") Integer projectId) {
        if (projectId == null) {
            return AjaxResult.error("项目ID不能为空");
        }

        try {
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long currentJudgeId = loginUser.getUserId();

            // 检查是否已经评分
            boolean hasScored = projectScoreService.hasJudgeScored(projectId, currentJudgeId);

            return AjaxResult.success(hasScored);
        } catch (Exception e) {
            return AjaxResult.error("检查评分状态失败：" + e.getMessage());
        }
    }

}
