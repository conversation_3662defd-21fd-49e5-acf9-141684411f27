package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysParentInfo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysParentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 父母信息Controller
 */
@RestController
@RequestMapping("/system/parentInfo")
public class SysParentInfoController extends BaseController {
    @Autowired
    private ISysParentInfoService sysParentInfoService;

    /**
     * 查询父母信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:parentInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysParentInfo sysParentInfo) {
        startPage();
        List<SysParentInfo> list = sysParentInfoService.selectSysParentInfoList(sysParentInfo);
        return getDataTable(list);
    }

    /**
     * 根据用户ID获取父母信息列表
     */
    @GetMapping("/user/{userId}")
    public AjaxResult getParentInfosByUserId(@PathVariable("userId") Long userId) {
        return success(sysParentInfoService.selectParentInfosByUserId(userId));
    }

    /**
     * 新增父母信息
     */
    @PreAuthorize("@ss.hasPermi('system:parentInfo:add')")
    @PostMapping
    public AjaxResult add(@RequestBody SysParentInfo sysParentInfo) {
        return toAjax(sysParentInfoService.insertSysParentInfo(sysParentInfo));
    }

    /**
     * 批量新增父母信息
     */
    @PreAuthorize("@ss.hasPermi('system:parentInfo:add')")
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<SysParentInfo> parentInfos) {
        return toAjax(sysParentInfoService.batchInsertParentInfo(parentInfos));
    }

    /**
     * 修改父母信息
     */
    @PreAuthorize("@ss.hasPermi('system:parentInfo:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody SysParentInfo sysParentInfo) {
        return toAjax(sysParentInfoService.updateSysParentInfo(sysParentInfo));
    }

    /**
     * 删除父母信息
     */
    @PreAuthorize("@ss.hasPermi('system:parentInfo:remove')")
    @DeleteMapping("/{parentIds}")
    public AjaxResult remove(@PathVariable List<Long> parentIds) {
        return toAjax(sysParentInfoService.deleteSysParentInfoByIds(parentIds));
    }

    /**
     * 根据用户ID删除父母信息
     */
    @PreAuthorize("@ss.hasPermi('system:parentInfo:remove')")
    @DeleteMapping("/user/{userId}")
    public AjaxResult removeByUserId(@PathVariable Long userId) {
        return toAjax(sysParentInfoService.deleteSysParentInfoByUserId(userId));
    }
} 