package com.ruoyi.web.controller.system;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.entity.SysParentInfo;
import com.ruoyi.common.core.domain.entity.SysTeamMember;
import com.ruoyi.system.service.ISysParentInfoService;
import com.ruoyi.system.service.ISysTeamMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import java.util.Arrays;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private TokenService tokenService;
    @Autowired
    private ISysTeamMemberService sysTeamMemberService;
    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private ISysParentInfoService parentInfoService;

    /**
     * 个人信息
     */
    @GetMapping
    public AjaxResult profile()
    {
        LoginUser loginUser = getLoginUser();
        SysUser sysUser = userService.selectUserById(loginUser.getUserId());
        SysUser user = loginUser.getUser();

        // 获取团队信息
        if (ObjectUtils.isNotEmpty(sysUser)) {
            List<SysTeamMember> sysTeamMembers = sysTeamMemberService.selectTeamMembersByUserId(sysUser.getUserId());
            System.out.println("团队信息 sysTeamMembers = " + sysTeamMembers);
            sysUser.setTeamMembers(sysTeamMembers);

            // 查询用户的父母信息
            List<SysParentInfo> parents = parentInfoService.selectParentInfosByUserId(sysUser.getUserId());
            sysUser.setParents(parents);
        }

        AjaxResult ajax = AjaxResult.success(sysUser);
        ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
        return ajax;
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user)
    {
        LoginUser loginUser = getLoginUser();
        SysUser currentUser = loginUser.getUser();
        currentUser.setNickName(user.getNickName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhonenumber(user.getPhonenumber());
        currentUser.setSex(user.getSex());
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
        }
        if (isValidEmail(user.getEmail())){
            return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱格式不正确");
        }

        if (userService.updateUserProfile(currentUser) > 0)
        {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    // 定义邮箱的正则表达式
    private static final String EMAIL_REGEX = "/^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\\.[a-zA-Z0-9-]{2,3}){1,2})$/";

    // 校验邮箱格式的方法
    public static boolean isValidEmail(String email) {
        return email != null && email.matches(EMAIL_REGEX);
    }
    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(@RequestBody Map<String, String> params)
    {
        String oldPassword = params.get("oldPassword");
        String newPassword = params.get("newPassword");
        LoginUser loginUser = getLoginUser();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userService.resetUserPwd(userName, newPassword) > 0)
        {
            // 更新缓存用户密码&密码最后更新时间
            loginUser.getUser().setPwdUpdateDate(DateUtils.getNowDate());
            loginUser.getUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception {
        // 新上传实现，上传到oss
        if (file.isEmpty()) {
            return AjaxResult.error("请选择图片！");
        }
        if (file.getSize() > 50 * 1024 * 1024) {
            return AjaxResult.error("文件大小不能超过限制！");
        }

        String originalFilename = file.getOriginalFilename();
        String suffix = StringUtils.substringAfterLast(originalFilename, ".");

        try {
            // 上传到阿里云
            String dateStr = getCurrentDateStr();

            // 根据文件扩展名获取正确的Content-Type
            String contentType = getContentType(suffix);

            FileInfo fileInfo = fileStorageService.of(file)
                    .setPath(dateStr + "/" + suffix + "/")
                    // 设置Content-Type为图片类型，确保浏览器正确识别
                    .putMetadata("Content-Type", contentType)
                    // 设置Content-Disposition为inline，让浏览器直接显示而不是下载
                    .putMetadata("Content-Disposition", "inline")
                    // 设置缓存控制，提高访问速度
                    .putMetadata("Cache-Control", "public, max-age=31536000")
                    // 如果需要支持跨域访问，添加CORS头
                    .putMetadata("Access-Control-Allow-Origin", "*")
                    .putMetadata("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
                    .putMetadata("Access-Control-Allow-Headers", "Content-Type, Authorization")
                    .upload();

            if (ObjectUtils.isEmpty(fileInfo)) {
                return AjaxResult.error("上传失败！");
            }

            String avatar = fileInfo.getUrl();
            LoginUser loginUser = getLoginUser();
            if (userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                loginUser.getUser().setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        } catch (Exception e) {
            log.error("上传文件发生异常：", e);
            return AjaxResult.error("上传异常，请联系管理员");
        }
        return AjaxResult.error("上传头像异常，请联系管理员");
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String suffix) {
        if (StringUtils.isEmpty(suffix)) {
            return "image/jpeg"; // 默认
        }

        switch (suffix.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "webp":
                return "image/webp";
            case "bmp":
                return "image/bmp";
            case "svg":
                return "image/svg+xml";
            default:
                return "image/jpeg"; // 默认为jpeg
        }
    }

    public static String getCurrentDateStr() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return currentDate.format(formatter);
    }
}
