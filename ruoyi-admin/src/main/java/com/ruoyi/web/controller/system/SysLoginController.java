package com.ruoyi.web.controller.system;

import java.util.*;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.annotation.RateLimiter;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysMenuService;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SysLoginController {

    /**
     * 短信发送-BlendID（随意自定义的）
     */
    public static final String TX_1 = "tx1";

    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions))
        {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        ajax.put("isDefaultModifyPwd", initPasswordIsModify(user.getPwdUpdateDate()));
        ajax.put("isPasswordExpired", passwordIsExpiration(user.getPwdUpdateDate()));
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    // 检查初始密码是否提醒修改
    public boolean initPasswordIsModify(Date pwdUpdateDate)
    {
        Integer initPasswordModify = Convert.toInt(configService.selectConfigByKey("sys.account.initPasswordModify"));
        return initPasswordModify != null && initPasswordModify == 1 && pwdUpdateDate == null;
    }

    // 检查密码是否过期
    public boolean passwordIsExpiration(Date pwdUpdateDate)
    {
        Integer passwordValidateDays = Convert.toInt(configService.selectConfigByKey("sys.account.passwordValidateDays"));
        if (passwordValidateDays != null && passwordValidateDays > 0)
        {
            if (StringUtils.isNull(pwdUpdateDate))
            {
                // 如果从未修改过初始密码，直接提醒过期
                return true;
            }
            Date nowDate = DateUtils.getNowDate();
            return DateUtils.differentDaysByMillisecond(nowDate, pwdUpdateDate) > passwordValidateDays;
        }
        return false;
    }

    /**
     * 获取用户实时信息
     * <p>上述接口是通过token获取，修改信息后要重新登录才更新</p>
     *
     * @return 用户信息
     */
    @GetMapping("getInfoLatest")
    public AjaxResult getInfoLatest() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        loginUser.setUser(userService.selectUserById(loginUser.getUserId()));
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions))
        {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        ajax.put("isDefaultModifyPwd", initPasswordIsModify(user.getPwdUpdateDate()));
        ajax.put("isPasswordExpired", passwordIsExpiration(user.getPwdUpdateDate()));
        return ajax;
    }

    /**
     * 发送短信验证码
     */
    @RateLimiter(key = "smsLimit:#{username}", count = 1, time = 60)
    @PostMapping("/sendVerifyCode")
    public AjaxResult sendVerifyCode(@RequestParam(name = "username") String username) {
        if (StringUtils.isBlank(username)) {
            return AjaxResult.error("请输入找回账号");
        }

        SysUser user = userService.selectUserByUserName(username);
        if (Objects.isNull(user)) {
            return AjaxResult.error("该账号不存在");
        }
        if (StringUtils.isBlank(user.getPhonenumber())) {
            return AjaxResult.error("该账号未绑定手机号");
        }

        // 检查Redis中是否存在未过期的验证码
        String verifyKey = CacheConstants.SMS_CODE_KEY + username;
        String existingCode = redisCache.getCacheObject(verifyKey);
        if (StringUtils.isNotBlank(existingCode)) {
            return AjaxResult.success("请使用已发送的验证码");
        }

        // 发送短信（优化日志记录）
        String verifyCode = RandomUtil.randomNumbers(4);
        try {
            SmsResponse response = SmsFactory.getSmsBlend(TX_1)
                    .sendMessage(user.getPhonenumber(), verifyCode);
            log.info("短信发送状态：{}, 手机尾号：{}", response.isSuccess(),
                    StringUtils.right(user.getPhonenumber(), 4));

            if (!response.isSuccess()) {
                return AjaxResult.error("发送失败，请重试");
            }

            // 存储验证码（设置5分钟过期）
            redisCache.setCacheObject(verifyKey, verifyCode, Constants.SMS_CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
            return AjaxResult.success("发送成功");
        } catch (Exception e) {
            log.error("短信发送异常（手机尾号:{}）",
                    StringUtils.right(user.getPhonenumber(), 4), e);
            return AjaxResult.error("系统繁忙，请稍后重试");
        }
    }

    /**
     * 找回密码
     */
    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(@RequestBody Map<String, String> reqMap) {
        String username = reqMap.get("username");
        String smsCode = reqMap.get("smsCode");
        String newPassword = reqMap.get("newPassword");
        String confirmPassword = reqMap.get("confirmPassword");
        if (StringUtils.isBlank(username)) {
            return AjaxResult.error("请输入账号");
        }
        if (StringUtils.isBlank(smsCode)) {
             return AjaxResult.error("请输入验证码");
        }
        if (StringUtils.isBlank(newPassword)) {
             return AjaxResult.error("请输入新密码");
        }

        if (!newPassword.equals(confirmPassword)) {
             return AjaxResult.error("两次输入密码不一致");
        }

        String verifyKey = CacheConstants.SMS_CODE_KEY + username;
        String verifyCode = redisCache.getCacheObject(verifyKey);
        if (StringUtils.isBlank(verifyCode)) {
            return AjaxResult.error("验证码已过期");
        }
        if (!verifyCode.equals(smsCode)) {
            return AjaxResult.error("验证码错误");
        }

        SysUser user = userService.selectUserByUserName(username);
        if (Objects.isNull(user)) {
            return AjaxResult.error("该账号不存在");
        }
        if (StringUtils.isBlank(user.getPhonenumber())) {
            return AjaxResult.error("该账号未绑定手机号");
        }

        user.setPassword(SecurityUtils.encryptPassword(newPassword));
        try {
            userService.updateUserProfile(user);
            redisCache.deleteObject(verifyKey);
            return AjaxResult.success("密码已重置");
        } catch (Exception e) {
            log.error("密码重置异常", e);
            return AjaxResult.error("系统繁忙，请重试");
        }
    }
}
