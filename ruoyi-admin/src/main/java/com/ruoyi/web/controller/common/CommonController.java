package com.ruoyi.web.controller.common;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.HashMap;
import java.nio.file.Files;
import java.nio.file.Paths;

import org.apache.commons.lang3.ObjectUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.upload.UploadPretreatment;
import org.dromara.x.file.storage.core.platform.MultipartUploadSupportInfo;
import org.dromara.x.file.storage.core.upload.FilePartInfo;
import org.dromara.x.file.storage.core.upload.FilePartInfoList;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import org.dromara.x.file.storage.core.platform.AliyunOssFileStorage;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.config.ServerConfig;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private FileStorageService fileStorageService;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）- 支持普通上传和分片上传
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > 500 * 1024 * 1024) {
            return AjaxResult.error("文件大小不能超过500MB！");
        }

        // 获取文件名并检查
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return AjaxResult.error("文件名不能为空");
        }

        try {
            String suffix = StringUtils.substringAfterLast(originalFilename, ".");
            // 获取不带后缀的文件名
            String filenameWithoutSuffix = StringUtils.substringBeforeLast(originalFilename, ".");
            // 拼接时间戳到文件名
            String newFilename = filenameWithoutSuffix + "_" + getCurrentTimeStr() + "." + suffix;

            // 检查存储服务是否可用
            if (fileStorageService == null) {
                log.error("文件存储服务未正确配置");
                return AjaxResult.error("文件存储服务未就绪，请联系管理员");
            }

            // 上传到OSS
            String dateStr = getCurrentDateStr();
            FileInfo fileInfo = fileStorageService.of(file)
                    .setPath(dateStr + "/" + suffix + "/")
                    .setSaveFilename(newFilename)
                    .upload();

            if (ObjectUtils.isEmpty(fileInfo)) {
                return AjaxResult.error("上传失败！");
            }

            log.info("文件上传成功：{}", fileInfo);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", fileInfo.getUrl());
            ajax.put("fileName", fileInfo.getFilename());
            ajax.put("newFileName", fileInfo.getPath());
            ajax.put("originalFilename", originalFilename);
            return ajax;
        } catch (Exception e) {
            log.error("上传文件发生异常：", e);
            return AjaxResult.error("上传异常：" + e.getMessage());
        }
    }

    /**
     * 验证文件是否已存在
     */
    @PostMapping("/verify")
    public AjaxResult verifyFile(@RequestBody Map<String, String> params) {
        String fileHash = params.get("fileHash");
        String fileName = params.get("fileName");

        if (StringUtils.isEmpty(fileHash) || StringUtils.isEmpty(fileName)) {
            return AjaxResult.error("参数不完整");
        }

        try {
            // 检查临时目录中是否存在已上传的分片
            String chunkDir = RuoYiConfig.getUploadPath() + File.separator + "chunks" + File.separator + fileHash;
            File dir = new File(chunkDir);

            // 检查是否有完整文件
            String filePath = RuoYiConfig.getUploadPath() + File.separator + fileHash + "_" + fileName;
            File completeFile = new File(filePath);

            Map<String, Object> data = new HashMap<>();
            if (completeFile.exists()) {
                // 文件已存在，可以直接使用
                data.put("shouldSkip", true);
                data.put("url", getFileUrl(fileHash + "_" + fileName));
                return AjaxResult.success(data);
            }

            // 获取已上传的分片列表
            List<Integer> uploadedChunks = new ArrayList<>();
            if (dir.exists() && dir.isDirectory()) {
                File[] chunks = dir.listFiles();
                if (chunks != null) {
                    for (File chunk : chunks) {
                        try {
                            int chunkIndex = Integer.parseInt(chunk.getName());
                            uploadedChunks.add(chunkIndex);
                        } catch (NumberFormatException e) {
                            // 忽略无效的分片文件名
                        }
                    }
                }
            }

            data.put("shouldSkip", false);
            data.put("uploadedChunks", uploadedChunks);
            return AjaxResult.success(data);

        } catch (Exception e) {
            log.error("验证文件时发生错误", e);
            return AjaxResult.error("验证文件失败");
        }
    }

    /**
     * 合并文件分片
     */
    @PostMapping("/merge")
    public AjaxResult mergeChunks(@RequestBody Map<String, String> params) {
        String fileHash = params.get("fileHash");
        String fileName = params.get("fileName");
        String totalChunksStr = params.get("totalChunks");

        if (StringUtils.isEmpty(fileHash) || StringUtils.isEmpty(fileName) || StringUtils.isEmpty(totalChunksStr)) {
            return AjaxResult.error("参数不完整");
        }

        try {
            int totalChunks = Integer.parseInt(totalChunksStr);
            String chunkDir = RuoYiConfig.getUploadPath() + File.separator + "chunks" + File.separator + fileHash;
            String targetFilePath = RuoYiConfig.getUploadPath() + File.separator + fileHash + "_" + fileName;

            // 检查所有分片是否都已上传
            File dir = new File(chunkDir);
            if (!dir.exists() || !dir.isDirectory()) {
                return AjaxResult.error("分片文件不存在");
            }

            File[] chunks = dir.listFiles();
            if (chunks == null || chunks.length != totalChunks) {
                return AjaxResult.error("分片文件不完整");
            }

            // 合并文件
            try (java.io.OutputStream out = Files.newOutputStream(Paths.get(targetFilePath))) {
                byte[] buffer = new byte[1024 * 1024]; // 1MB buffer
                for (int i = 0; i < totalChunks; i++) {
                    File chunk = new File(chunkDir, String.valueOf(i));
                    if (!chunk.exists()) {
                        return AjaxResult.error("分片" + i + "不存在");
                    }

                    try (java.io.InputStream in = Files.newInputStream(chunk.toPath())) {
                        int len;
                        while ((len = in.read(buffer)) != -1) {
                            out.write(buffer, 0, len);
                        }
                    }
                }
            }

            // 清理分片文件
            try {
                Files.walk(Paths.get(chunkDir))
                    .sorted((p1, p2) -> -p1.compareTo(p2)) // 反向排序，确保先删除文件，后删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.error("删除文件失败: " + path, e);
                        }
                    });
            } catch (IOException e) {
                log.error("清理分片文件失败", e);
                // 继续执行，不影响主流程
            }

            // 上传到OSS
            File mergedFile = new File(targetFilePath);
            FileInfo fileInfo = fileStorageService.of(mergedFile)
                    .setPath(getCurrentDateStr() + "/" + StringUtils.substringAfterLast(fileName, ".") + "/")
                    .setSaveFilename(fileHash + "_" + fileName)
                    .upload();

            if (ObjectUtils.isEmpty(fileInfo)) {
                return AjaxResult.error("上传失败");
            }

            // 删除本地合并文件
            FileUtils.deleteFile(targetFilePath);

            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", fileInfo.getUrl());
            return ajax;

        } catch (NumberFormatException e) {
            return AjaxResult.error("无效的分片数量");
        } catch (IOException e) {
            log.error("合并文件时发生错误", e);
            return AjaxResult.error("合并文件失败");
        } catch (Exception e) {
            log.error("处理文件时发生错误", e);
            return AjaxResult.error("处理文件失败");
        }
    }

    /**
     * 获取文件URL
     */
    private String getFileUrl(String fileName) {
        return serverConfig.getUrl() + Constants.RESOURCE_PREFIX + "/" + fileName;
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    public static String getCurrentDateStr() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return currentDate.format(formatter);
    }

    /**
     * 获取当前时间字符串，格式为yyMMddHHmmss
     * 例如：2024-11-21 11:22:11 -> 241121112211
     *
     * @return 格式化后的时间字符串
     */
    public static String getCurrentTimeStr() {
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        return currentDateTime.format(formatter);
    }

    // ==================== 手动分片上传相关接口 ====================

    /**
     * 检查是否支持手动分片上传
     */
    @GetMapping("/multipart/support")
    public AjaxResult checkMultipartUploadSupport() {
        try {
            MultipartUploadSupportInfo supportInfo = fileStorageService.isSupportMultipartUpload();

            Map<String, Object> data = new HashMap<>();
            data.put("isSupport", supportInfo.getIsSupport());
            data.put("isSupportListParts", supportInfo.getIsSupportListParts());
            data.put("isSupportAbort", supportInfo.getIsSupportAbort());
            data.put("platform", fileStorageService.getDefaultPlatform());

            return AjaxResult.success(data);
        } catch (Exception e) {
            log.error("检查分片上传支持时发生错误", e);
            return AjaxResult.error("检查分片上传支持失败");
        }
    }

    /**
     * 初始化手动分片上传
     */
    @PostMapping("/multipart/init")
    public AjaxResult initMultipartUpload(@RequestBody Map<String, Object> params) {
        try {
            String originalFilename = (String) params.get("originalFilename");
            Long fileSize = Long.valueOf(params.get("fileSize").toString());

            if (StringUtils.isEmpty(originalFilename)) {
                return AjaxResult.error("文件名不能为空");
            }

            if (fileSize == null || fileSize <= 0) {
                return AjaxResult.error("文件大小无效");
            }

            // 检查是否支持分片上传
            MultipartUploadSupportInfo supportInfo = fileStorageService.isSupportMultipartUpload();
            if (!supportInfo.getIsSupport()) {
                return AjaxResult.error("当前存储平台不支持手动分片上传");
            }

            String suffix = StringUtils.substringAfterLast(originalFilename, ".");
            String filenameWithoutSuffix = StringUtils.substringBeforeLast(originalFilename, ".");
            String newFilename = filenameWithoutSuffix + "_" + getCurrentTimeStr() + "." + suffix;
            String dateStr = getCurrentDateStr();

            // 初始化分片上传
            FileInfo fileInfo = fileStorageService.initiateMultipartUpload()
                    .setPath(dateStr + "/" + suffix + "/")
                    .setOriginalFilename(originalFilename)
                    .setSaveFilename(newFilename)
                    .setSize(fileSize)
                    .init();

            if (ObjectUtils.isEmpty(fileInfo)) {
                return AjaxResult.error("初始化分片上传失败");
            }

            log.info("手动分片上传初始化成功：{}", fileInfo);

            Map<String, Object> data = new HashMap<>();
            data.put("uploadId", fileInfo.getUploadId());
            data.put("fileId", fileInfo.getId());
            data.put("platform", fileInfo.getPlatform());
            data.put("path", fileInfo.getPath());
            data.put("filename", fileInfo.getFilename());
            data.put("basePath", fileInfo.getBasePath());
            data.put("originalFilename", fileInfo.getOriginalFilename());
            data.put("size", fileInfo.getSize());
            data.put("ext", fileInfo.getExt());
            data.put("url", fileInfo.getUrl());

            return AjaxResult.success(data);
        } catch (Exception e) {
            log.error("初始化分片上传时发生错误", e);
            return AjaxResult.error("初始化分片上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传分片
     */
    @PostMapping("/multipart/upload")
    public AjaxResult uploadPart(
            @RequestParam("file") MultipartFile file,
            @RequestParam("uploadId") String uploadId,
            @RequestParam("partNumber") Integer partNumber,
            @RequestParam("fileId") String fileId,
            @RequestParam(value = "platform", required = false) String platform,
            @RequestParam(value = "path", required = false) String path,
            @RequestParam(value = "filename", required = false) String filename) {

        try {
            if (file == null || file.isEmpty()) {
                return AjaxResult.error("分片文件不能为空");
            }

            if (StringUtils.isEmpty(uploadId)) {
                return AjaxResult.error("uploadId不能为空");
            }

            if (partNumber == null || partNumber < 1) {
                return AjaxResult.error("分片号无效");
            }

            // 构建完整的FileInfo对象用于分片上传
            FileInfo fileInfo = new FileInfo();
            fileInfo.setId(fileId);
            fileInfo.setUploadId(uploadId);

            // 设置必要的平台和路径信息
            if (StringUtils.isNotEmpty(platform)) {
                fileInfo.setPlatform(platform);
            } else {
                fileInfo.setPlatform(fileStorageService.getDefaultPlatform());
            }

            if (StringUtils.isNotEmpty(path)) {
                fileInfo.setPath(path);
            }

            if (StringUtils.isNotEmpty(filename)) {
                fileInfo.setFilename(filename);
            }

            // 设置基础路径（从配置中获取）
            fileInfo.setBasePath("file/");

            // 上传分片
            FilePartInfo filePartInfo = fileStorageService
                    .uploadPart(fileInfo, partNumber, file, file.getSize())
                    .upload();

            if (ObjectUtils.isEmpty(filePartInfo)) {
                return AjaxResult.error("分片上传失败");
            }

            log.info("分片上传成功：partNumber={}, size={}, eTag={}",
                    partNumber, file.getSize(), filePartInfo.getETag());

            Map<String, Object> data = new HashMap<>();
            data.put("partNumber", filePartInfo.getPartNumber());
            data.put("eTag", filePartInfo.getETag());
            data.put("partSize", filePartInfo.getPartSize());

            return AjaxResult.success(data);
        } catch (Exception e) {
            log.error("上传分片时发生错误：partNumber={}", partNumber, e);
            return AjaxResult.error("分片上传失败：" + e.getMessage());
        }
    }

    /**
     * 完成分片上传
     */
    @PostMapping("/multipart/complete")
    public AjaxResult completeMultipartUpload(@RequestBody Map<String, Object> params) {
        try {
            String uploadId = (String) params.get("uploadId");
            String fileId = (String) params.get("fileId");
            String platform = (String) params.get("platform");
            String path = (String) params.get("path");
            String filename = (String) params.get("filename");
            String basePath = (String) params.get("basePath");
            String originalFilename = (String) params.get("originalFilename");
            Object sizeObj = params.get("size");
            String ext = (String) params.get("ext");
            String url = (String) params.get("url");

            if (StringUtils.isEmpty(uploadId)) {
                return AjaxResult.error("uploadId不能为空");
            }

            // 构建完整的FileInfo对象
            FileInfo fileInfo = new FileInfo();
            fileInfo.setId(fileId);
            fileInfo.setUploadId(uploadId);

            // 设置必要的平台和路径信息
            if (StringUtils.isNotEmpty(platform)) {
                fileInfo.setPlatform(platform);
            } else {
                fileInfo.setPlatform(fileStorageService.getDefaultPlatform());
            }

            if (StringUtils.isNotEmpty(path)) {
                fileInfo.setPath(path);
            }

            if (StringUtils.isNotEmpty(filename)) {
                fileInfo.setFilename(filename);
            }

            if (StringUtils.isNotEmpty(basePath)) {
                fileInfo.setBasePath(basePath);
            } else {
                fileInfo.setBasePath("file/");
            }

            if (StringUtils.isNotEmpty(originalFilename)) {
                fileInfo.setOriginalFilename(originalFilename);
            }

            if (sizeObj != null) {
                if (sizeObj instanceof Number) {
                    fileInfo.setSize(((Number) sizeObj).longValue());
                } else if (sizeObj instanceof String) {
                    try {
                        fileInfo.setSize(Long.parseLong((String) sizeObj));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析文件大小: {}", sizeObj);
                    }
                }
            }

            if (StringUtils.isNotEmpty(ext)) {
                fileInfo.setExt(ext);
            }

            if (StringUtils.isNotEmpty(url)) {
                fileInfo.setUrl(url);
            }

            // 完成分片上传 - 使用最简化的FileInfo对象
            FileInfo completeFileInfo = new FileInfo();
            completeFileInfo.setId(fileId);
            completeFileInfo.setUploadId(uploadId);
            completeFileInfo.setPlatform(platform != null ? platform : fileStorageService.getDefaultPlatform());
            completeFileInfo.setPath(path);
            completeFileInfo.setFilename(filename);
            completeFileInfo.setBasePath(basePath != null ? basePath : "file/");
            if (StringUtils.isNotEmpty(url)) {
                completeFileInfo.setUrl(url);
            }

            // 完成分片上传并获取最终的文件URL
            String finalUrl = null;
            try {
                FileInfo completedFileInfo = fileStorageService.completeMultipartUpload(completeFileInfo).complete();
                finalUrl = completedFileInfo.getUrl();
                log.info("使用x-file-storage完成分片上传成功，URL: {}", finalUrl);
            } catch (Exception e) {
                log.warn("使用x-file-storage完成分片上传失败，尝试使用原生OSS SDK: {}", e.getMessage());

                // 使用原生阿里云OSS SDK完成分片上传
                if (fileStorageService.getFileStorage() instanceof AliyunOssFileStorage) {
                    AliyunOssFileStorage aliyunStorage = (AliyunOssFileStorage) fileStorageService.getFileStorage();
                    OSS ossClient = aliyunStorage.getClient();
                    String bucketName = aliyunStorage.getBucketName();
                    String objectKey = completeFileInfo.getBasePath() + completeFileInfo.getPath() + completeFileInfo.getFilename();
                    log.info("OSS完成上传参数 - bucketName: {}, objectKey: {}", bucketName, objectKey);

                    // 列举已上传的分片，获取partETags
                    ListPartsRequest listPartsRequest = new ListPartsRequest(bucketName, objectKey, uploadId);
                    PartListing partListing = ossClient.listParts(listPartsRequest);
                    List<PartETag> partETags = new ArrayList<>();
                    for (PartSummary partSummary : partListing.getParts()) {
                        partETags.add(new PartETag(partSummary.getPartNumber(), partSummary.getETag()));
                    }
                    log.info("获取到 {} 个已上传的分片", partETags.size());

                    // 创建完成分片上传请求，提供所有分片的ETag
                    CompleteMultipartUploadRequest completeRequest =
                        new CompleteMultipartUploadRequest(bucketName, objectKey, uploadId, partETags);

                    CompleteMultipartUploadResult result = ossClient.completeMultipartUpload(completeRequest);
                    log.info("使用原生OSS SDK完成分片上传成功，ETag: {}", result.getETag());

                    // 构建最终的文件访问URL
                    // 使用阿里云OSS的标准URL格式：https://bucket-name.endpoint/object-key
                    // 从配置中获取endpoint
                    String endpoint = "oss-cn-beijing.aliyuncs.com"; // 从配置文件中读取
                    finalUrl = "https://" + bucketName + "." + endpoint + "/" + objectKey;
                    log.info("构建的文件访问URL: {}", finalUrl);
                } else {
                    throw e; // 如果不是阿里云OSS，重新抛出异常
                }
            }

            log.info("手动分片上传完成：uploadId={}, finalUrl={}", uploadId, finalUrl);

            Map<String, Object> data = new HashMap<>();
            data.put("url", finalUrl);
            data.put("filename", completeFileInfo.getFilename());

            return AjaxResult.success(data);
        } catch (Exception e) {
            log.error("完成分片上传时发生错误", e);
            return AjaxResult.error("完成分片上传失败：" + e.getMessage());
        }
    }

    /**
     * 列举已上传的分片
     */
    @PostMapping("/multipart/list")
    public AjaxResult listParts(@RequestBody Map<String, Object> params) {
        try {
            String uploadId = (String) params.get("uploadId");
            String fileId = (String) params.get("fileId");
            String platform = (String) params.get("platform");
            String path = (String) params.get("path");
            String filename = (String) params.get("filename");

            if (StringUtils.isEmpty(uploadId)) {
                return AjaxResult.error("uploadId不能为空");
            }

            // 检查是否支持列举分片
            MultipartUploadSupportInfo supportInfo = fileStorageService.isSupportMultipartUpload();
            if (!supportInfo.getIsSupportListParts()) {
                return AjaxResult.error("当前存储平台不支持列举已上传的分片");
            }

            // 构建完整的FileInfo对象
            FileInfo fileInfo = new FileInfo();
            fileInfo.setId(fileId);
            fileInfo.setUploadId(uploadId);

            // 设置必要的平台和路径信息
            if (StringUtils.isNotEmpty(platform)) {
                fileInfo.setPlatform(platform);
            } else {
                fileInfo.setPlatform(fileStorageService.getDefaultPlatform());
            }

            if (StringUtils.isNotEmpty(path)) {
                fileInfo.setPath(path);
            }

            if (StringUtils.isNotEmpty(filename)) {
                fileInfo.setFilename(filename);
            }

            // 设置基础路径（从配置中获取）
            fileInfo.setBasePath("file/");

            // 列举已上传的分片
            FilePartInfoList partList = fileStorageService.listParts(fileInfo).listParts();

            Map<String, Object> data = new HashMap<>();
            data.put("parts", partList.getList());
            data.put("totalCount", partList.getList().size());

            return AjaxResult.success(data);
        } catch (Exception e) {
            log.error("列举已上传分片时发生错误", e);
            return AjaxResult.error("列举已上传分片失败：" + e.getMessage());
        }
    }

    /**
     * 取消分片上传
     */
    @PostMapping("/multipart/abort")
    public AjaxResult abortMultipartUpload(@RequestBody Map<String, Object> params) {
        try {
            String uploadId = (String) params.get("uploadId");
            String fileId = (String) params.get("fileId");
            String platform = (String) params.get("platform");
            String path = (String) params.get("path");
            String filename = (String) params.get("filename");
            String basePath = (String) params.get("basePath");
            String originalFilename = (String) params.get("originalFilename");
            Object sizeObj = params.get("size");
            String ext = (String) params.get("ext");
            String url = (String) params.get("url");

            if (StringUtils.isEmpty(uploadId)) {
                return AjaxResult.error("uploadId不能为空");
            }

            // 检查是否支持取消上传
            MultipartUploadSupportInfo supportInfo = fileStorageService.isSupportMultipartUpload();
            if (!supportInfo.getIsSupportAbort()) {
                return AjaxResult.error("当前存储平台不支持取消分片上传");
            }

            // 构建完整的FileInfo对象
            FileInfo fileInfo = new FileInfo();
            fileInfo.setId(fileId);
            fileInfo.setUploadId(uploadId);

            // 设置必要的平台和路径信息
            if (StringUtils.isNotEmpty(platform)) {
                fileInfo.setPlatform(platform);
            } else {
                fileInfo.setPlatform(fileStorageService.getDefaultPlatform());
            }

            if (StringUtils.isNotEmpty(path)) {
                fileInfo.setPath(path);
            }

            if (StringUtils.isNotEmpty(filename)) {
                fileInfo.setFilename(filename);
            }

            if (StringUtils.isNotEmpty(basePath)) {
                fileInfo.setBasePath(basePath);
            } else {
                fileInfo.setBasePath("file/");
            }

            if (StringUtils.isNotEmpty(originalFilename)) {
                fileInfo.setOriginalFilename(originalFilename);
            }

            if (sizeObj != null) {
                if (sizeObj instanceof Number) {
                    fileInfo.setSize(((Number) sizeObj).longValue());
                } else if (sizeObj instanceof String) {
                    try {
                        fileInfo.setSize(Long.parseLong((String) sizeObj));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析文件大小: {}", sizeObj);
                    }
                }
            }

            if (StringUtils.isNotEmpty(ext)) {
                fileInfo.setExt(ext);
            }

            if (StringUtils.isNotEmpty(url)) {
                fileInfo.setUrl(url);
            }

            // 取消分片上传
            fileStorageService.abortMultipartUpload(fileInfo).abort();

            log.info("手动分片上传已取消：uploadId={}", uploadId);

            return AjaxResult.success("分片上传已取消");
        } catch (Exception e) {
            log.error("取消分片上传时发生错误", e);
            return AjaxResult.error("取消分片上传失败：" + e.getMessage());
        }
    }

}
