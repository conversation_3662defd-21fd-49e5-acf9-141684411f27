# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
#  profile: /home/<USER>/uploadPath
  profile: /Users/<USER>/Documents/code/business/250601-ruoyi-mn/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 1000MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  # redis 配置
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 11
    # 密码
    password:
    # 连接超时时间
    timeout: 30000
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 5
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 50
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 300

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5tSTSQ4D7TJqHmy1WUhg
    access-key-secret: ******************************
    bucket-name: bjqsnai

# 创作者声明模板
file:
  student: https://bjqsnai.oss-cn-beijing.aliyuncs.com/templates/%E5%88%9B%E4%BD%9C%E8%80%85%E5%A3%B0%E6%98%8E%E6%A8%A1%E6%9D%BF-%E5%AD%A6%E7%94%9F.pdf
  teacher: https://bjqsnai.oss-cn-beijing.aliyuncs.com/templates/%E5%88%9B%E4%BD%9C%E8%80%85%E5%A3%B0%E6%98%8E%E6%A8%A1%E6%9D%BF-%E8%80%81%E5%B8%88.pdf
  consentBook: https://bjqsnai.oss-cn-beijing.aliyuncs.com/templates/%E6%B4%BB%E5%8A%A8%E5%90%8C%E6%84%8F%E4%B9%A6.docx
  declaration: https://bjqsnai.oss-cn-beijing.aliyuncs.com/file/20250608/pdf/684509cf17ce984fb3528017.pdf
#  declarationPath: /files/declaration.xlsx
  declarationPath: /Users/<USER>/Documents/temp/0618/declaration.xlsx
  excelTempPath: /files/temp/excel/
  pdfTempPath: /files/temp/pdf/

# 评分评语
judge:
  comment1: "非常不错的作品！"
  comment2: "这个作品风格独特！"
  comment3: "一款极具个性化的作品！"

# 短信发送
sms:
  # 标注从yml读取配置
  config-type: yaml
  blends:
    tx1:
      supplier: alibaba
      access-key-id: LTAI5tSTSQ4D7TJqHmy1WUhg
      access-key-secret: ******************************
      signature: 北京佳艺星尚文化传媒
      template-id: SMS_487390613
      templateName: code

# 存储配置
dromara:
  x-file-storage:
    default-platform: aliyun-oss
    thumbnail-suffix: .min.jpg
    aliyun-oss:
      - platform: aliyun-oss
        enable-storage: true
        access-key: LTAI5tSTSQ4D7TJqHmy1WUhg
        secret-key: ******************************
        end-point: oss-cn-beijing.aliyuncs.com
        bucket-name: bjqsnai
        domain: https://bjqsnai.oss-cn-beijing.aliyuncs.com/
        base-path: file/
